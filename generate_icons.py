#!/usr/bin/env python3
"""
Generate PWA icons for MyBar app
"""

from PIL import Image, ImageDraw, ImageFont
import os

def create_icon(size, output_path):
    """Create a simple icon with drink emoji and gradient background"""
    
    # Create image with gradient background
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Create gradient background
    for y in range(size):
        # Gradient from #667eea to #764ba2
        ratio = y / size
        r = int(102 + (118 - 102) * ratio)  # 667eea to 764ba2
        g = int(126 + (75 - 126) * ratio)
        b = int(234 + (162 - 234) * ratio)
        
        draw.line([(0, y), (size, y)], fill=(r, g, b, 255))
    
    # Add rounded corners
    mask = Image.new('L', (size, size), 0)
    mask_draw = ImageDraw.Draw(mask)
    corner_radius = size // 8
    mask_draw.rounded_rectangle(
        [(0, 0), (size, size)], 
        radius=corner_radius, 
        fill=255
    )
    
    # Apply mask for rounded corners
    img.putalpha(mask)
    
    # Add drink emoji or text
    try:
        # Try to use a font for the emoji
        font_size = size // 2
        font = ImageFont.truetype("seguiemj.ttf", font_size)  # Windows emoji font
        text = "🍹"
    except:
        # Fallback to default font with text
        font_size = size // 3
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
        text = "MB"  # MyBar initials
    
    # Get text size and center it
    bbox = draw.textbbox((0, 0), text, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    # Add white shadow for better visibility
    shadow_offset = max(1, size // 100)
    draw.text((x + shadow_offset, y + shadow_offset), text, font=font, fill=(0, 0, 0, 100))
    draw.text((x, y), text, font=font, fill=(255, 255, 255, 255))
    
    # Save the icon
    img.save(output_path, 'PNG', optimize=True)
    print(f"✅ Created icon: {output_path} ({size}x{size})")

def main():
    """Generate all required PWA icons"""
    
    # Icon sizes for PWA
    sizes = [16, 32, 72, 96, 128, 144, 152, 192, 384, 512]
    
    # Create icons directory
    icons_dir = "bar/static/bar/icons"
    os.makedirs(icons_dir, exist_ok=True)
    
    print("🎨 Generating PWA icons...")
    
    for size in sizes:
        output_path = os.path.join(icons_dir, f"icon-{size}x{size}.png")
        create_icon(size, output_path)
    
    # Create additional icons for shortcuts
    shortcut_icons = {
        "shortcut-order.png": "🍸",
        "shortcut-bartender.png": "👨‍🍳", 
        "shortcut-bar.png": "🧪",
        "badge-72x72.png": "🍹"
    }
    
    print("\n🎯 Generating shortcut icons...")
    
    for filename, emoji in shortcut_icons.items():
        output_path = os.path.join(icons_dir, filename)
        create_shortcut_icon(96, output_path, emoji)
    
    print(f"\n✅ All icons generated in {icons_dir}/")
    print("📱 Your PWA is ready to install!")

def create_shortcut_icon(size, output_path, emoji):
    """Create shortcut icons with specific emojis"""
    
    img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
    draw = ImageDraw.Draw(img)
    
    # Simpler background for shortcuts
    draw.ellipse([(0, 0), (size, size)], fill=(102, 126, 234, 255))
    
    # Add emoji
    try:
        font_size = size // 2
        font = ImageFont.truetype("seguiemj.ttf", font_size)
    except:
        font_size = size // 3
        try:
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            font = ImageFont.load_default()
        emoji = "MB"  # Fallback
    
    bbox = draw.textbbox((0, 0), emoji, font=font)
    text_width = bbox[2] - bbox[0]
    text_height = bbox[3] - bbox[1]
    
    x = (size - text_width) // 2
    y = (size - text_height) // 2
    
    draw.text((x, y), emoji, font=font, fill=(255, 255, 255, 255))
    
    img.save(output_path, 'PNG', optimize=True)
    print(f"✅ Created shortcut icon: {output_path}")

if __name__ == "__main__":
    main()
