from django.core.management.base import BaseCommand
from django.contrib.auth.models import User


class Command(BaseCommand):
    help = 'Opretter tablet1 bruger med simpel adgangskode'

    def handle(self, *args, **options):
        username = 'tablet1'
        password = 'tablet1'

        # Delete existing user if exists
        if User.objects.filter(username=username).exists():
            User.objects.filter(username=username).delete()
            self.stdout.write(
                self.style.WARNING(f'Eksisterende bruger "{username}" slettet')
            )

        # Create user
        user = User.objects.create_user(
            username=username,
            password=password,
            email='<EMAIL>'
        )

        self.stdout.write(
            self.style.SUCCESS(f'<PERSON><PERSON><PERSON> "{username}" oprettet med adgangskode "{password}"')
        )
