# Generated by Django 5.1.7 on 2025-08-16 20:35

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0025_new_request_system'),
    ]

    operations = [
        migrations.RemoveField(
            model_name='tabletprofile',
            name='bartenders',
        ),
        migrations.RemoveField(
            model_name='tabletprofile',
            name='created_by',
        ),
        migrations.AlterModelOptions(
            name='drink',
            options={'ordering': ['name']},
        ),
        migrations.AlterModelOptions(
            name='ingredient',
            options={'ordering': ['name']},
        ),
        migrations.AlterField(
            model_name='drink',
            name='name',
            field=models.CharField(db_index=True, max_length=100),
        ),
        migrations.AlterField(
            model_name='ingredient',
            name='name',
            field=models.CharField(db_index=True, max_length=100),
        ),
        migrations.AddIndex(
            model_name='drink',
            index=models.Index(fields=['drink_type'], name='bar_drink_drink_t_d73ba2_idx'),
        ),
        migrations.AddIndex(
            model_name='drink',
            index=models.Index(fields=['is_featured'], name='bar_drink_is_feat_55bd5e_idx'),
        ),
        migrations.DeleteModel(
            name='TabletProfile',
        ),
    ]
