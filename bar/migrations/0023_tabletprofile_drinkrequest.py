# Generated by Django 5.1.7 on 2025-08-16 18:36

import django.db.models.deletion
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0022_alter_rating_unique_together_remove_rating_drink_and_more'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.CreateModel(
            name='TabletProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Navn på tablet/computer (f.eks. 'Simons 50 års fødselsdag')", max_length=100)),
                ('description', models.TextField(blank=True, help_text='Beskrivelse af eventet')),
                ('is_active', models.BooleanField(default=True, help_text='Kun aktive profiler kan modtage drink requests', verbose_name='Aktiv')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('bartenders', models.ManyToManyField(blank=True, help_text='Brugere der skal kunne se drink requests fra denne tablet', related_name='tablet_profiles', to=settings.AUTH_USER_MODEL)),
                ('created_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='created_tablet_profiles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Tablet Profil',
                'verbose_name_plural': 'Tablet Profiler',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DrinkRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('guest_name', models.CharField(blank=True, help_text='Gæstens navn (valgfrit)', max_length=100)),
                ('table_number', models.CharField(blank=True, help_text='Bordnummer (valgfrit)', max_length=20)),
                ('special_notes', models.TextField(blank=True, help_text='Særlige ønsker eller noter')),
                ('status', models.CharField(choices=[('pending', 'Afventer'), ('in_progress', 'I gang'), ('completed', 'Færdig'), ('cancelled', 'Annulleret')], default='pending', max_length=20)),
                ('requested_at', models.DateTimeField(auto_now_add=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('assigned_bartender', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='assigned_drink_requests', to=settings.AUTH_USER_MODEL)),
                ('drink', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requests', to='bar.drink')),
                ('tablet_profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='drink_requests', to='bar.tabletprofile')),
            ],
            options={
                'verbose_name': 'Drink Request',
                'verbose_name_plural': 'Drink Requests',
                'ordering': ['-requested_at'],
            },
        ),
    ]
