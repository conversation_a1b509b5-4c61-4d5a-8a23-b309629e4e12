# Generated manually to create new request system

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import django.utils.timezone


class Migration(migrations.Migration):

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('bar', '0024_drop_old_request_models'),
    ]

    operations = [
        migrations.CreateModel(
            name='DrinkRequestProfile',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(help_text="Navn på profilen (f.eks. 'Simons 50 års fødselsdag')", max_length=100)),
                ('is_active', models.BooleanField(default=True, verbose_name='Aktiv')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('bartender_user', models.ForeignKey(help_text='Brugeren der kan se og håndtere requests', on_delete=django.db.models.deletion.CASCADE, related_name='bartender_profiles', to=settings.AUTH_USER_MODEL)),
                ('requester_user', models.ForeignKey(help_text='Brugeren der kan bestille drinks', on_delete=django.db.models.deletion.CASCADE, related_name='request_profiles', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Drink Request Profil',
                'verbose_name_plural': 'Drink Request Profiler',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='DrinkRequest',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('guest_name', models.CharField(blank=True, help_text='Gæstens navn (valgfrit)', max_length=100)),
                ('table_number', models.CharField(blank=True, help_text='Bordnummer (valgfrit)', max_length=20)),
                ('special_notes', models.TextField(blank=True, help_text='Særlige ønsker eller noter')),
                ('status', models.CharField(choices=[('pending', 'Afventer'), ('in_progress', 'I gang'), ('completed', 'Færdig'), ('cancelled', 'Annulleret')], default='pending', max_length=20)),
                ('requested_at', models.DateTimeField(auto_now_add=True)),
                ('started_at', models.DateTimeField(blank=True, null=True)),
                ('completed_at', models.DateTimeField(blank=True, null=True)),
                ('drink', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='requests', to='bar.drink')),
                ('profile', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='drink_requests', to='bar.drinkrequestprofile')),
                ('requester', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='my_drink_requests', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Drink Request',
                'verbose_name_plural': 'Drink Requests',
                'ordering': ['-requested_at'],
            },
        ),
    ]
