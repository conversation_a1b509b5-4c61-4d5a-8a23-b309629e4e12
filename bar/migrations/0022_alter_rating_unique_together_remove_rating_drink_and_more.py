# Generated by Django 5.2.5 on 2025-08-08 16:18

import django.db.models.deletion
import django.utils.timezone
from django.conf import settings
from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0021_fix_decimal_places'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
    ]

    operations = [
        migrations.AlterUniqueTogether(
            name='rating',
            unique_together=None,
        ),
        migrations.RemoveField(
            model_name='rating',
            name='drink',
        ),
        migrations.RemoveField(
            model_name='rating',
            name='user',
        ),
        migrations.CreateModel(
            name='DrinkFavorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('is_favorite', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('drink', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='legacy_favorites', to='bar.drink')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Drink Favorit',
                'verbose_name_plural': 'Drink Favoritter',
                'ordering': ['-updated_at'],
                'unique_together': {('user', 'drink')},
            },
        ),
        migrations.CreateModel(
            name='DrinkRating',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('rating', models.IntegerField(choices=[(1, 1), (2, 2), (3, 3), (4, 4), (5, 5)])),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('drink', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='ratings', to='bar.drink')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Drink Bedømmelse',
                'verbose_name_plural': 'Drink Bedømmelser',
                'ordering': ['-updated_at'],
                'unique_together': {('user', 'drink')},
            },
        ),
        migrations.CreateModel(
            name='DrinkViewHistory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('viewed_at', models.DateTimeField(default=django.utils.timezone.now)),
                ('drink', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='view_history', to='bar.drink')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Drink Visningshistorik',
                'verbose_name_plural': 'Drink Visningshistorik',
                'ordering': ['-viewed_at'],
            },
        ),
        migrations.CreateModel(
            name='UserFavorite',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('drink', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorited_by', to='bar.drink')),
                ('user', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='favorites', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'verbose_name': 'Bruger favorit',
                'verbose_name_plural': 'Bruger favoritter',
                'ordering': ['-created_at'],
                'unique_together': {('user', 'drink')},
            },
        ),
        migrations.DeleteModel(
            name='Favorite',
        ),
        migrations.DeleteModel(
            name='Rating',
        ),
    ]
