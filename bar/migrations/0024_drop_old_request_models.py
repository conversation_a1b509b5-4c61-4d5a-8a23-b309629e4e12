# Generated manually to drop old request models

from django.db import migrations


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0023_tabletprofile_drinkrequest'),
    ]

    operations = [
        migrations.RunSQL("DROP TABLE IF EXISTS bar_drinkrequest;"),
        migrations.RunSQL("DROP TABLE IF EXISTS bar_tabletprofile_bartenders;"),
        migrations.RunSQL("DROP TABLE IF EXISTS bar_tabletprofile;"),
    ]
