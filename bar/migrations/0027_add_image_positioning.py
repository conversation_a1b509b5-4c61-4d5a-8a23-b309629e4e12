# Generated by Django 5.1.7 on 2025-08-17 20:32

from django.db import migrations, models


class Migration(migrations.Migration):

    dependencies = [
        ('bar', '0026_remove_tabletprofile_bartenders_and_more'),
    ]

    operations = [
        migrations.AddField(
            model_name='drink',
            name='image_position_x',
            field=models.IntegerField(default=50, help_text='Horizontal position af billedet (0-100%). 50% = center', verbose_name='Billede position X (%)'),
        ),
        migrations.AddField(
            model_name='drink',
            name='image_position_y',
            field=models.IntegerField(default=50, help_text='Vertikal position af billedet (0-100%). 50% = center', verbose_name='Billede position Y (%)'),
        ),
        migrations.AddField(
            model_name='drink',
            name='image_zoom',
            field=models.IntegerField(default=100, help_text='Zoom niveau af billedet (50-200%). 100% = normal størrelse', verbose_name='<PERSON><PERSON> zoom (%)'),
        ),
    ]
