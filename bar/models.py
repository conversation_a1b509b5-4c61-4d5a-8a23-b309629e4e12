from django.db import models
from django.contrib.auth.models import User
from ckeditor.fields import RichTextField
from django.conf import settings
from django.utils import timezone
from django.core.validators import MinValueValidator, MaxValueValidator


class Ingredient(models.Model):
    name = models.CharField(max_length=100, db_index=True)  # Index for faster searches
    image = models.ImageField(upload_to='ingredient_images/', null=True, blank=True)
    description = models.TextField(blank=True, help_text='Kort beskrivelse af ingrediensen')
    substitutes = models.ManyToManyField('self', blank=True, symmetrical=False,
                                       help_text="Ingredienser der kan erstatte denne",
                                       verbose_name="Erstatninger")

    class Meta:
        ordering = ['name']  # Default ordering for consistent results

    def __str__(self):
        return self.name

    def get_all_substitutes(self):
        """Returnerer alle ingredienser der kan erstatte denne (inkl. sig selv)"""
        substitutes = list(self.substitutes.all())
        substitutes.append(self)
        return substitutes

class IngredientGroup(models.Model):
    name = models.CharField(max_length=100)
    ingredients = models.ManyToManyField(Ingredient)

    def __str__(self):
        return self.name


class DrinkIngredientGroup(models.Model):
    """Specific ingredient group amounts for each drink"""
    drink = models.ForeignKey('Drink', on_delete=models.CASCADE, related_name='drink_ingredient_groups')
    ingredient_group = models.ForeignKey(IngredientGroup, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=6, decimal_places=2, help_text="Mængde i cl/ml (f.eks. 0.75, 2.50)")
    unit = models.CharField(max_length=10, default='cl', choices=[
        ('cl', 'Centiliter'),
        ('ml', 'Milliliter'),
        ('stk', 'Stykker'),
        ('skiver', 'Skiver'),
        ('tsk', 'Teskeer'),
        ('spsk', 'Spiseskeer'),
    ])
    is_required = models.BooleanField(default=True, verbose_name="Påkrævet",
                                    help_text="Markér hvis denne ingrediens gruppe er påkrævet for drinken")

    class Meta:
        unique_together = ('drink', 'ingredient_group')

    def __str__(self):
        required_text = " (påkrævet)" if self.is_required else " (valgfri)"
        return f"{self.drink.name}: {self.amount} {self.unit} {self.ingredient_group.name}{required_text}"

class Drink(models.Model):
    DRINK_TYPES = [
        ('drink', 'Drink'),
        ('cocktail', 'Cocktail'),
        ('shot', 'Shot'),
        ('nonalcoholic', 'Alkoholfri'),
    ]

    drink_type = models.CharField(
        max_length=20,
        choices=DRINK_TYPES,
        default='drink'
    )
    name = models.CharField(max_length=100, db_index=True)  # Index for faster searches
    required_ingredients = models.ManyToManyField(Ingredient, blank=True)
    ingredient_groups = models.ManyToManyField(IngredientGroup, blank=True)
    recipe = RichTextField(help_text="Instruktioner og ekstra noter (ingredienser tilføjes automatisk fra 'Drink ingredients' nedenfor)")
    image = models.ImageField(upload_to='drink_images/', null=True, blank=True)

    # Image positioning and display options
    image_position_x = models.IntegerField(
        default=50,
        verbose_name="Billede position X (%)",
        help_text="Horizontal position af billedet (0-100%). 50% = center"
    )
    image_position_y = models.IntegerField(
        default=50,
        verbose_name="Billede position Y (%)",
        help_text="Vertikal position af billedet (0-100%). 50% = center"
    )
    image_zoom = models.IntegerField(
        default=100,
        verbose_name="Billede zoom (%)",
        help_text="Zoom niveau af billedet (50-200%). 100% = normal størrelse"
    )

    is_featured = models.BooleanField(default=False, verbose_name="Featured",
                                    help_text="Mark this drink as featured on the homepage")

    class Meta:
        ordering = ['name']  # Default ordering for consistent results
        indexes = [
            models.Index(fields=['drink_type']),
            models.Index(fields=['is_featured']),
        ]

    def __str__(self):
        return self.name

    def get_average_rating(self):
        avg = self.ratings.aggregate(models.Avg('rating'))['rating__avg']
        return round(float(avg), 1) if avg is not None else 0.0

    def get_full_recipe(self):
        """Combines drink ingredients and ingredient groups with manual recipe instructions"""
        full_recipe = ""

        # Add ingredients section
        drink_ingredients = self.drink_ingredients.all()
        drink_ingredient_groups = self.drink_ingredient_groups.all()

        if drink_ingredients or drink_ingredient_groups:
            full_recipe += "<h6>Ingredienser:</h6><ul>"

            # Add specific ingredients
            for ingredient in drink_ingredients:
                full_recipe += f"<li>{ingredient.amount} {ingredient.unit} {ingredient.ingredient.name}</li>"

            # Add ingredient groups
            for group_item in drink_ingredient_groups:
                group_ingredients = ", ".join([ing.name for ing in group_item.ingredient_group.ingredients.all()])
                full_recipe += f"<li>{group_item.amount} {group_item.unit} {group_item.ingredient_group.name} ({group_ingredients})</li>"

            full_recipe += "</ul>"

        # Add manual recipe if exists
        if self.recipe.strip():
            if drink_ingredients or drink_ingredient_groups:
                full_recipe += "<h6>Fremgangsmåde:</h6>"
            full_recipe += self.recipe

        return full_recipe if full_recipe else self.recipe


class DrinkIngredient(models.Model):
    """Specific ingredient amounts for each drink"""
    drink = models.ForeignKey(Drink, on_delete=models.CASCADE, related_name='drink_ingredients')
    ingredient = models.ForeignKey(Ingredient, on_delete=models.CASCADE)
    amount = models.DecimalField(max_digits=6, decimal_places=2, help_text="Mængde i cl/ml (f.eks. 0.75, 2.50)")
    unit = models.CharField(max_length=10, default='cl', choices=[
        ('cl', 'Centiliter'),
        ('ml', 'Milliliter'),
        ('stk', 'Stykker'),
        ('skiver', 'Skiver'),
        ('tsk', 'Teskeer'),
        ('spsk', 'Spiseskeer'),
    ])
    is_required = models.BooleanField(default=True, verbose_name="Påkrævet",
                                    help_text="Markér hvis denne ingrediens er påkrævet for drinken")

    class Meta:
        unique_together = ('drink', 'ingredient')

    def __str__(self):
        required_text = " (påkrævet)" if self.is_required else " (valgfri)"
        return f"{self.drink.name}: {self.amount} {self.unit} {self.ingredient.name}{required_text}"


# Signal to automatically update required_ingredients
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

@receiver([post_save, post_delete], sender=DrinkIngredient)
def update_drink_required_ingredients(sender, instance, **kwargs):
    """
    Når en DrinkIngredient gemmes eller slettes, opdateres
    den overordnede Drinks required_ingredients M2M-felt automatisk.
    Inkluderer både påkrævede DrinkIngredients og alle ingredienser fra ingredient groups.
    """
    drink = instance.drink
    required_ingredients = []

    # Tilføj kun påkrævede ingredienser fra DrinkIngredient
    # Ingredient groups håndteres separat i view logikken
    required_ingredients = [di.ingredient for di in drink.drink_ingredients.filter(is_required=True)]

    # Sæt M2M-feltet til kun påkrævede DrinkIngredients
    drink.required_ingredients.set(required_ingredients)


@receiver([post_save, post_delete], sender=DrinkIngredientGroup)
def update_drink_ingredient_groups(sender, instance, **kwargs):
    """
    Når en DrinkIngredientGroup gemmes eller slettes, opdater ingredient_groups M2M
    """
    drink = instance.drink
    # Få alle ingredient groups fra DrinkIngredientGroup
    ingredient_groups = [dig.ingredient_group for dig in drink.drink_ingredient_groups.all()]
    # Opdater M2M-feltet
    drink.ingredient_groups.set(ingredient_groups)

# Signal for når ingredient groups ændres på en drink
from django.db.models.signals import m2m_changed

@receiver(m2m_changed, sender=Drink.ingredient_groups.through)
def update_drink_required_ingredients_from_groups(sender, instance, action, **kwargs):
    """
    Når ingredient groups ændres på en drink, opdater required_ingredients
    """
    if action in ['post_add', 'post_remove', 'post_clear']:
        required_ingredients = []

        # Tilføj kun påkrævede ingredienser fra DrinkIngredient
        # Ingredient groups håndteres separat i view logikken
        required_ingredients = [di.ingredient for di in instance.drink_ingredients.filter(is_required=True)]

        # Sæt M2M-feltet til kun påkrævede DrinkIngredients
        instance.required_ingredients.set(required_ingredients)

class UserBar(models.Model):
    user = models.OneToOneField(User, on_delete=models.CASCADE)
    ingredients = models.ManyToManyField(Ingredient, blank=True)

class Comment(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    drink = models.ForeignKey(Drink, on_delete=models.CASCADE, related_name='comments')
    text = models.TextField()
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        ordering = ['-created_at'] # Nyeste kommentarer først

    def __str__(self):
        return f"Kommentar af {self.user.username} på {self.drink.name}"


# DRINK PLANNER MODELS - Easy to remove if not wanted
class DrinkPlan(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE)
    name = models.CharField(max_length=100, help_text="Navn på din fest/event")
    guest_count = models.PositiveIntegerField(help_text="Antal gæster")
    created_at = models.DateTimeField(auto_now_add=True)

    def __str__(self):
        return f"{self.name} ({self.guest_count} gæster)"


class DrinkPlanItem(models.Model):
    plan = models.ForeignKey(DrinkPlan, on_delete=models.CASCADE, related_name='items')
    drink = models.ForeignKey(Drink, on_delete=models.CASCADE)
    servings_per_guest = models.DecimalField(max_digits=3, decimal_places=1, default=1.0,
                                           help_text="Hvor mange af denne drink per gæst")

    def total_servings(self):
        return int(self.servings_per_guest * self.plan.guest_count)

    def __str__(self):
        return f"{self.drink.name} for {self.plan.name}"

# MODELLER KONSOLIDERET FRA bar/models/ MAPPE

class UserFavorite(models.Model):
    """Model til at holde styr på brugerens favorit drinks."""
    user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='favorites')
    drink = models.ForeignKey('Drink', on_delete=models.CASCADE, related_name='favorited_by')
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        unique_together = ('user', 'drink')
        verbose_name = 'Bruger favorit'
        verbose_name_plural = 'Bruger favoritter'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.user.username} - {self.drink.name}"

class DrinkRating(models.Model):
    """Model for drink bedømmelser."""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    drink = models.ForeignKey('Drink', on_delete=models.CASCADE, related_name='ratings')
    rating = models.IntegerField(choices=[(i, i) for i in range(1, 6)])
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('user', 'drink')
        verbose_name = 'Drink Bedømmelse'
        verbose_name_plural = 'Drink Bedømmelser'
        ordering = ['-updated_at']

    def __str__(self):
        return f"{self.user.username}: {self.drink.name} - {self.rating} stjerner"

class DrinkFavorite(models.Model):
    """Model for at markere en drink som favorit."""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    drink = models.ForeignKey('Drink', on_delete=models.CASCADE, related_name='legacy_favorites') # Changed related_name to avoid clash
    is_favorite = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        unique_together = ('user', 'drink')
        verbose_name = 'Drink Favorit'
        verbose_name_plural = 'Drink Favoritter'
        ordering = ['-updated_at']

    def __str__(self):
        status = "Favorit" if self.is_favorite else "Ikke favorit"
        return f"{self.user.username}: {self.drink.name} - {status}"

class DrinkViewHistory(models.Model):
    """Model for at spore sete drinks."""
    user = models.ForeignKey(settings.AUTH_USER_MODEL, on_delete=models.CASCADE)
    drink = models.ForeignKey('Drink', on_delete=models.CASCADE, related_name='view_history')
    viewed_at = models.DateTimeField(default=timezone.now)

    class Meta:
        verbose_name = 'Drink Visningshistorik'
        verbose_name_plural = 'Drink Visningshistorik'
        ordering = ['-viewed_at']


# SIMPLIFIED DRINK REQUEST SYSTEM
class DrinkRequestProfile(models.Model):
    """Simpel profil for drink requests - en bruger der kan bestille, en der kan se requests"""
    name = models.CharField(max_length=100, help_text="Navn på profilen (f.eks. 'Simons 50 års fødselsdag')")
    requester_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='request_profiles',
                                     help_text="Brugeren der kan bestille drinks")
    bartender_user = models.ForeignKey(User, on_delete=models.CASCADE, related_name='bartender_profiles',
                                     help_text="Brugeren der kan se og håndtere requests")
    is_active = models.BooleanField(default=True, verbose_name="Aktiv")
    created_at = models.DateTimeField(auto_now_add=True)

    class Meta:
        verbose_name = 'Drink Request Profil'
        verbose_name_plural = 'Drink Request Profiler'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.name} ({self.requester_user.username} → {self.bartender_user.username})"


class DrinkRequest(models.Model):
    """Model for drink requests"""
    STATUS_CHOICES = [
        ('pending', 'Afventer'),
        ('in_progress', 'I gang'),
        ('completed', 'Færdig'),
        ('cancelled', 'Annulleret'),
    ]

    profile = models.ForeignKey(DrinkRequestProfile, on_delete=models.CASCADE, related_name='drink_requests')
    drink = models.ForeignKey(Drink, on_delete=models.CASCADE, related_name='requests')
    requester = models.ForeignKey(User, on_delete=models.CASCADE, related_name='my_drink_requests')
    guest_name = models.CharField(max_length=100, blank=True, help_text="Gæstens navn (valgfrit)")
    table_number = models.CharField(max_length=20, blank=True, help_text="Bordnummer (valgfrit)")
    special_notes = models.TextField(blank=True, help_text="Særlige ønsker eller noter")
    status = models.CharField(max_length=20, choices=STATUS_CHOICES, default='pending')

    # Timestamps
    requested_at = models.DateTimeField(auto_now_add=True)
    started_at = models.DateTimeField(null=True, blank=True)
    completed_at = models.DateTimeField(null=True, blank=True)

    class Meta:
        verbose_name = 'Drink Request'
        verbose_name_plural = 'Drink Requests'
        ordering = ['-requested_at']

    def __str__(self):
        guest_info = f" til {self.guest_name}" if self.guest_name else ""
        return f"{self.drink.name}{guest_info} - {self.get_status_display()}"

    def get_status_emoji(self):
        """Returnerer emoji baseret på status"""
        status_emojis = {
            'pending': '⏳',
            'in_progress': '🔄',
            'completed': '✅',
            'cancelled': '❌',
        }
        return status_emojis.get(self.status, '❓')