{% extends 'bar/base.html' %}
{% load static %}

{% block title %}Offline - MyBar{% endblock %}

{% block content %}
<div class="container-fluid">
    <div class="row justify-content-center align-items-center min-vh-100">
        <div class="col-md-6 text-center">
            <div class="offline-content">
                <div class="offline-icon mb-4">
                    <img src="{% static 'bar/icons/icon-192x192.png' %}" alt="MyBar" style="width: 120px; height: 120px; border-radius: 20px;">
                </div>
                
                <h1 class="display-4 mb-3">📶 Offline</h1>
                <p class="lead mb-4">Du er ikke forbundet til internettet lige nu.</p>
                
                <div class="offline-features mb-4">
                    <h5>Du kan stadig:</h5>
                    <ul class="list-unstyled">
                        <li class="mb-2">
                            <i class="fas fa-eye text-primary me-2"></i>
                            Se tidligere besøgte sider
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-bookmark text-primary me-2"></i>
                            Gennemse cachede drinks
                        </li>
                        <li class="mb-2">
                            <i class="fas fa-clock text-primary me-2"></i>
                            Dine requests gemmes til du kommer online
                        </li>
                    </ul>
                </div>
                
                <div class="connection-status mb-4">
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-wifi me-2"></i>
                        <span id="connection-text">Tjekker forbindelse...</span>
                    </div>
                </div>
                
                <div class="offline-actions">
                    <button onclick="checkConnection()" class="btn btn-primary btn-lg me-3">
                        <i class="fas fa-sync-alt me-2"></i>
                        Prøv igen
                    </button>
                    
                    <button onclick="goBack()" class="btn btn-outline-secondary btn-lg">
                        <i class="fas fa-arrow-left me-2"></i>
                        Gå tilbage
                    </button>
                </div>
                
                <div class="mt-5">
                    <small class="text-muted">
                        MyBar fungerer bedst med internetforbindelse, men mange funktioner virker offline.
                    </small>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
.offline-content {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 40px;
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.offline-icon img {
    filter: grayscale(0.3);
    transition: filter 0.3s ease;
}

.offline-icon img:hover {
    filter: grayscale(0);
}

.offline-features ul li {
    background: rgba(102, 126, 234, 0.1);
    padding: 10px 15px;
    border-radius: 10px;
    border-left: 4px solid #667eea;
}

.connection-status {
    position: relative;
}

.connection-pulse {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% { opacity: 1; }
    50% { opacity: 0.5; }
    100% { opacity: 1; }
}

.btn {
    border-radius: 15px;
    padding: 12px 24px;
    font-weight: 600;
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

@media (max-width: 768px) {
    .offline-content {
        padding: 30px 20px;
        margin: 20px;
    }
    
    .btn-lg {
        width: 100%;
        margin-bottom: 10px;
    }
}
</style>

<script>
function checkConnection() {
    const button = event.target;
    const originalText = button.innerHTML;
    
    button.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Tjekker...';
    button.disabled = true;
    
    // Check if we're back online
    if (navigator.onLine) {
        // Try to fetch a small resource to verify connection
        fetch('/', { method: 'HEAD', cache: 'no-cache' })
            .then(() => {
                showToast('✅ Forbindelse genoprettet!', 'success');
                setTimeout(() => {
                    window.location.href = '/';
                }, 1000);
            })
            .catch(() => {
                showConnectionStatus('Stadig offline', 'warning');
                button.innerHTML = originalText;
                button.disabled = false;
            });
    } else {
        showConnectionStatus('Ingen internetforbindelse', 'danger');
        button.innerHTML = originalText;
        button.disabled = false;
    }
}

function goBack() {
    if (window.history.length > 1) {
        window.history.back();
    } else {
        window.location.href = '/';
    }
}

function showConnectionStatus(message, type) {
    const statusElement = document.getElementById('connection-text');
    const alertElement = statusElement.closest('.alert');
    
    statusElement.textContent = message;
    alertElement.className = `alert alert-${type}`;
    
    if (type === 'success') {
        alertElement.classList.add('connection-pulse');
    } else {
        alertElement.classList.remove('connection-pulse');
    }
}

// Monitor connection status
window.addEventListener('online', () => {
    showConnectionStatus('Forbindelse genoprettet! Opdaterer...', 'success');
    setTimeout(() => {
        window.location.reload();
    }, 2000);
});

window.addEventListener('offline', () => {
    showConnectionStatus('Forbindelse tabt', 'danger');
});

// Initial connection check
document.addEventListener('DOMContentLoaded', () => {
    if (navigator.onLine) {
        showConnectionStatus('Forbindelse tilgængelig', 'info');
    } else {
        showConnectionStatus('Ingen internetforbindelse', 'danger');
    }
});
</script>
{% endblock %}
