{% extends 'bar/base.html' %}
{% load static %}

{% block title %}Min Bar - MyBar{% endblock %}

{% block content %}
<div class="container-fluid px-0">
    <!-- Enhanced Control Bar -->
    <div class="enhanced-control-bar" data-aos="fade-down">
        <div class="control-container">
            <div class="control-left">
                <h4 class="control-title">⚙️ Rediger Min Bar</h4>
                <div class="ingredient-stats">
                    <span class="stat-badge" id="selected-count">{{ selected_ids|length }}</span>
                    <span class="stat-text">af {{ ingredients|length }} valgt</span>
                </div>
            </div>

            <div class="control-center">
                <div class="search-wrapper">
                    <input type="text" class="search-input" placeholder="🔍 Søg ingredienser..." id="searchInput">
                    <div class="search-icon">🔍</div>
                </div>

                <div class="filter-tabs">
                    <button class="filter-tab active" data-filter="all">Alle</button>
                    <button class="filter-tab" data-filter="spirits">🥃 Spiritus</button>
                    <button class="filter-tab" data-filter="mixers">🥤 Mixere</button>
                    <button class="filter-tab" data-filter="fruits">🍋 Frugter</button>
                </div>
            </div>

            <div class="control-right">
                <div class="grid-controls">
                    <label class="grid-label">Layout:</label>
                    <select id="gridSizeSelect" class="grid-select" onchange="updateGridSize(this.value)">
                        <option value="4">4 kolonner</option>
                        <option value="6">6 kolonner</option>
                        <option value="8" selected>8 kolonner</option>
                        <option value="10">10 kolonner</option>
                        <option value="12">12 kolonner</option>
                    </select>
                </div>

                <div class="action-buttons">
                    <button onclick="selectAll()" class="action-btn primary" title="Vælg alle">
                        <span class="btn-icon">✅</span>
                    </button>
                    <button onclick="clearAll()" class="action-btn secondary" title="Ryd alle">
                        <span class="btn-icon">❌</span>
                    </button>
                    <button onclick="randomSelection()" class="action-btn accent" title="Tilfældig">
                        <span class="btn-icon">🎲</span>
                    </button>
                    <button type="submit" form="bar-form" class="action-btn success" title="Gem Min Bar">
                        <span class="btn-icon">💾</span>
                        <span class="btn-text">Gem</span>
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Ingredient Grid -->
    <form method="post" id="bar-form">
        {% csrf_token %}
        <div class="ingredient-grid size-8" id="ingredient-list">
            {% for ingredient in ingredients %}
            <div class="ingredient-item" data-category="{% if 'vodka' in ingredient.name.lower or 'gin' in ingredient.name.lower or 'whiskey' in ingredient.name.lower or 'tequila' in ingredient.name.lower or 'rom' in ingredient.name.lower %}spirits{% elif 'juice' in ingredient.name.lower or 'tonic' in ingredient.name.lower or 'sprite' in ingredient.name.lower or 'cola' in ingredient.name.lower %}mixers{% elif 'lime' in ingredient.name.lower or 'citron' in ingredient.name.lower or 'appelsin' in ingredient.name.lower %}fruits{% else %}other{% endif %}">

                <div class="ingredient-card {% if ingredient.id in selected_ids %}selected{% endif %}" data-ingredient-id="{{ ingredient.id }}">
                    <input type="checkbox" name="ingredients" value="{{ ingredient.id }}"
                           {% if ingredient.id in selected_ids %}checked{% endif %} style="display: none;">

                    <!-- Selection Indicator -->
                    <div class="selection-indicator">
                        <div class="checkmark">✓</div>
                    </div>

                    <!-- Universal Touch-Friendly Info Button -->
                    <div class="info-button" onclick="showIngredientInfo(this, event)"
                         onmouseenter="showTooltip(this)" onmouseleave="hideTooltip(this)">
                        <span class="info-icon">ℹ️</span>
                    </div>

                    {% if ingredient.image %}
                        <img data-src="{{ ingredient.image.url }}" alt="{{ ingredient.name }}" class="ingredient-card-img lazy" loading="lazy">
                    {% else %}
                        <div class="ingredient-card-img no-image">
                            <span class="no-image-icon">📦</span>
                        </div>
                    {% endif %}

                    <div class="card-content">
                        <h3 class="ingredient-name">{{ ingredient.name }}</h3>
                        {% if ingredient.category %}
                            <div class="ingredient-category">{{ ingredient.category }}</div>
                        {% endif %}
                    </div>

                    <!-- Hidden description for mobile popup -->
                    <div class="hidden-description" style="display: none;">
                        {% if ingredient.description %}{{ ingredient.description }}{% else %}{{ ingredient.name }} - Klik for at vælge denne ingrediens{% endif %}
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </form>

    <!-- Floating Save Button -->
    <div class="floating-save-btn" id="floatingSaveBtn" style="display: none;">
        <button type="submit" form="bar-form" class="fab-save-btn">
            <span class="fab-icon">💾</span>
            <span class="fab-text">Gem Min Bar</span>
            <div class="fab-counter" id="fabCounter">0</div>
        </button>
    </div>
</div>

<style>
/* Enhanced CSS with Dark/Light Theme Support */
:root {
    --primary-color: #667eea;
    --secondary-color: #764ba2;
    --accent-color: #f093fb;
    --success-color: #4ecdc4;
    --warning-color: #ffd93d;
    --danger-color: #ff6b6b;

    /* Light theme */
    --bg-primary: #ffffff;
    --bg-secondary: #f8f9fa;
    --bg-tertiary: #e9ecef;
    --text-primary: #212529;
    --text-secondary: #6c757d;
    --text-muted: #adb5bd;
    --border-color: #dee2e6;
    --shadow-light: rgba(0, 0, 0, 0.1);
    --shadow-medium: rgba(0, 0, 0, 0.15);
    --shadow-heavy: rgba(0, 0, 0, 0.25);
}

/* Dark theme */
[data-bs-theme="dark"] {
    --bg-primary: #212529;
    --bg-secondary: #343a40;
    --bg-tertiary: #495057;
    --text-primary: #f8f9fa;
    --text-secondary: #adb5bd;
    --text-muted: #6c757d;
    --border-color: #495057;
    --shadow-light: rgba(255, 255, 255, 0.1);
    --shadow-medium: rgba(255, 255, 255, 0.15);
    --shadow-heavy: rgba(255, 255, 255, 0.25);
}

/* Lightweight Control Bar */
.enhanced-control-bar {
    background: var(--bg-primary);
    border: 1px solid var(--border-color);
    border-radius: 15px;
    padding: 15px;
    margin: 0 -15px 20px -15px;
    box-shadow: 0 4px 16px var(--shadow-light);
    position: sticky;
    top: 10px;
    z-index: 1000;
    transition: all 0.3s ease;
}

.control-container {
    display: flex;
    align-items: center;
    justify-content: space-between;
    gap: 20px;
    flex-wrap: wrap;
}

.control-left {
    display: flex;
    align-items: center;
    gap: 15px;
    min-width: 200px;
}

.control-title {
    color: var(--text-primary);
    font-weight: 700;
    font-size: 1.3rem;
    margin: 0;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.ingredient-stats {
    display: flex;
    align-items: center;
    gap: 8px;
}

.stat-badge {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    padding: 6px 12px;
    border-radius: 20px;
    font-weight: 600;
    font-size: 0.9rem;
}

.stat-text {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
}

.control-center {
    flex: 1;
    max-width: 400px;
}

.search-wrapper {
    position: relative;
    margin-bottom: 15px;
}

.search-input {
    width: 100%;
    padding: 12px 20px 12px 45px;
    border: 2px solid var(--border-color);
    border-radius: 25px;
    background: var(--bg-primary);
    color: var(--text-primary);
    font-size: 1rem;
    transition: all 0.3s ease;
}

.search-input:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.search-icon {
    position: absolute;
    left: 15px;
    top: 50%;
    transform: translateY(-50%);
    color: var(--text-muted);
    font-size: 1.1rem;
}

.filter-tabs {
    display: flex;
    gap: 8px;
    flex-wrap: wrap;
}

.filter-tab {
    background: var(--bg-secondary);
    border: 1px solid var(--border-color);
    color: var(--text-secondary);
    padding: 6px 12px;
    border-radius: 15px;
    font-size: 0.8rem;
    cursor: pointer;
    transition: all 0.3s ease;
    white-space: nowrap;
}

.filter-tab:hover,
.filter-tab.active {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px var(--shadow-medium);
}

.control-right {
    display: flex;
    align-items: center;
    gap: 15px;
}

.grid-controls {
    display: flex;
    align-items: center;
    gap: 8px;
}

.grid-label {
    color: var(--text-secondary);
    font-size: 0.9rem;
    font-weight: 500;
    margin: 0;
}

.grid-select {
    background: var(--bg-primary);
    border: 2px solid var(--border-color);
    color: var(--text-primary);
    padding: 8px 12px;
    border-radius: 10px;
    font-size: 0.9rem;
    cursor: pointer;
    transition: all 0.3s ease;
}

.grid-select:focus {
    outline: none;
    border-color: var(--primary-color);
}

.action-buttons {
    display: flex;
    gap: 8px;
}

.action-btn {
    min-width: 45px;
    height: 45px;
    border: none;
    border-radius: 25px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 5px;
    font-size: 1.2rem;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
    padding: 0 12px;
}

.action-btn .btn-text {
    font-size: 0.85rem;
    font-weight: 600;
    white-space: nowrap;
}

.action-btn .btn-icon {
    font-size: 1.1rem;
}

.action-btn.primary {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    color: white;
}

.action-btn.secondary {
    background: linear-gradient(135deg, var(--text-muted), var(--text-secondary));
    color: white;
}

.action-btn.accent {
    background: linear-gradient(135deg, var(--accent-color), var(--warning-color));
    color: white;
}

.action-btn.success {
    background: linear-gradient(135deg, var(--success-color), #26d0ce);
    color: white;
}

.action-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 10px 25px var(--shadow-medium);
}

.action-btn:active {
    transform: translateY(-1px) scale(0.98);
}

/* Enhanced Ingredient Grid - Full Width with Performance */
.ingredient-grid {
    display: grid;
    gap: 15px;
    padding: 0;
    margin: 0;
    width: 100vw;
    margin-left: calc(-50vw + 50%);
    padding-left: 15px;
    padding-right: 15px;
    box-sizing: border-box;
    /* Performance optimizations */
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
    perspective: 1000px;
}

/* Grid size classes - Full width utilization with responsive heights */
.ingredient-grid.size-4 {
    grid-template-columns: repeat(4, 1fr);
    gap: 20px;
}

.ingredient-grid.size-6 {
    grid-template-columns: repeat(6, 1fr);
    gap: 15px;
}

.ingredient-grid.size-8 {
    grid-template-columns: repeat(8, 1fr);
    gap: 12px;
}

.ingredient-grid.size-10 {
    grid-template-columns: repeat(10, 1fr);
    gap: 10px;
}

.ingredient-grid.size-12 {
    grid-template-columns: repeat(12, 1fr);
    gap: 8px;
}

.ingredient-item {
    position: relative;
    height: 220px; /* Default height */
}

/* Responsive heights for different grid sizes */
.ingredient-grid.size-4 .ingredient-item { height: 280px; }
.ingredient-grid.size-6 .ingredient-item { height: 240px; }
.ingredient-grid.size-8 .ingredient-item { height: 220px; }
.ingredient-grid.size-10 .ingredient-item { height: 180px; }
.ingredient-grid.size-12 .ingredient-item { height: 160px; }

/* ULTRA-FAST CARD STYLING - Minimal CSS */
.ingredient-card {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 15px;
    box-shadow: 0 4px 16px rgba(31, 38, 135, 0.2);
    border: 1px solid rgba(255, 255, 255, 0.18);
    overflow: hidden;
    cursor: pointer;
    transition: transform 0.15s ease;
    position: relative;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    transform: translateZ(0);
}

.ingredient-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 24px rgba(31, 38, 135, 0.3);
}

/* Selected state styling */
.ingredient-card.selected {
    background: linear-gradient(135deg, #4ecdc4 0%, #44a08d 100%);
    box-shadow: 0 12px 40px rgba(78, 205, 196, 0.4);
    transform: translateY(-3px);
}

.ingredient-card.selected .selection-indicator {
    opacity: 1;
    transform: scale(1);
}

/* Selection Indicator */
.selection-indicator {
    position: absolute;
    top: 10px;
    left: 10px;
    width: 30px;
    height: 30px;
    background: rgba(255, 255, 255, 0.9);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
    z-index: 10;
}

.checkmark {
    color: #4ecdc4;
    font-weight: bold;
    font-size: 1.1rem;
}

/* Info Button */
.info-button {
    position: absolute;
    top: 10px;
    right: 10px;
    width: 30px;
    height: 30px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
    cursor: help;
    z-index: 10;
}

.flip-card:hover .info-button {
    opacity: 1;
    transform: scale(1);
}

.info-icon {
    color: white;
    font-size: 0.9rem;
}

.card-header {
    position: absolute;
    top: 10px;
    left: 10px;
    right: 10px;
    display: flex;
    justify-content: space-between;
    z-index: 10;
}

.selection-indicator {
    width: 30px;
    height: 30px;
    background: var(--success-color);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
}

.ingredient-card.selected .selection-indicator {
    opacity: 1;
    transform: scale(1);
}

.checkmark {
    color: white;
    font-weight: bold;
    font-size: 1rem;
}

.info-button {
    width: 30px;
    height: 30px;
    background: rgba(0, 0, 0, 0.7);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transform: scale(0);
    transition: all 0.3s ease;
    cursor: help;
}

.ingredient-card:hover .info-button {
    opacity: 1;
    transform: scale(1);
}

.info-icon {
    color: white;
    font-size: 0.9rem;
}

/* Ingredient Card Image - Fixed Proportions with Lazy Loading */
.ingredient-card-img {
    width: 100%;
    height: 75%;
    object-fit: contain;
    object-position: center center;
    border-radius: 15px 15px 0 0;
    transition: all 0.3s ease;
    padding: 10px;
    box-sizing: border-box;
    background: linear-gradient(135deg, rgba(255,255,255,0.1), rgba(255,255,255,0.05));
}

/* Lazy loading states */
.ingredient-card-img.lazy {
    opacity: 0;
    transition: opacity 0.3s ease;
}

.ingredient-card-img.lazy.loaded {
    opacity: 1;
}

.ingredient-card-img.lazy:not(.loaded) {
    background: rgba(255,255,255,0.1);
}

.ingredient-card:hover .ingredient-card-img {
    transform: scale(1.02);
}

/* Ensure all images use contain for proper proportions */
.ingredient-card-img[src*="hansen"],
.ingredient-card-img[src*="Hansen"],
.ingredient-card-img[src*="champagne"],
.ingredient-card-img[src*="Champagne"],
.ingredient-card-img[src*="cinzano"],
.ingredient-card-img[src*="Cinzano"],
.ingredient-card-img[src*="cucullo"],
.ingredient-card-img[src*="Cucullo"],
.ingredient-card-img[src*="prosecco"],
.ingredient-card-img[src*="Prosecco"],
.ingredient-card-img[src*="bottle"],
.ingredient-card-img[src*="Bottle"],
.ingredient-card-img[src*="rom"],
.ingredient-card-img[src*="Rom"],
.ingredient-card-img[src*="vodka"],
.ingredient-card-img[src*="Vodka"],
.ingredient-card-img[src*="whiskey"],
.ingredient-card-img[src*="Whiskey"],
.ingredient-card-img[src*="gin"],
.ingredient-card-img[src*="Gin"] {
    object-fit: contain;
    object-position: center center;
    padding: 15px;
}

.ingredient-card-img.no-image {
    background: linear-gradient(135deg, rgba(255,255,255,0.2), rgba(255,255,255,0.1));
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 15px 15px 0 0;
}

.no-image-icon {
    font-size: 3rem;
    color: rgba(255,255,255,0.8);
}

/* Card Content */
.card-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 10px;
    border-radius: 0 0 15px 15px;
    text-align: center;
}

.ingredient-name {
    font-size: 0.9rem;
    font-weight: 600;
    margin: 0 0 5px 0;
    color: white;
    text-shadow: 0 1px 3px rgba(0, 0, 0, 0.8);
    line-height: 1.2;
}

.ingredient-category {
    font-size: 0.7rem;
    color: rgba(255,255,255,0.8);
    background: rgba(255,255,255,0.2);
    padding: 2px 6px;
    border-radius: 8px;
    display: inline-block;
    margin: 0;
}

/* Desktop/Mobile Info Button Visibility */
.desktop-only {
    display: block;
}

.mobile-only {
    display: none;
}

@media (max-width: 768px) {
    .desktop-only {
        display: none;
    }

    .mobile-only {
        display: block;
    }
}

/* Mobile Info Popup */
.mobile-info-popup {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.9);
    color: white;
    padding: 20px;
    border-radius: 15px;
    max-width: 300px;
    width: 90%;
    z-index: 10000;
    text-align: center;
    box-shadow: 0 10px 30px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.mobile-info-popup h4 {
    margin: 0 0 10px 0;
    color: #4ecdc4;
    font-size: 1.1rem;
}

.mobile-info-popup p {
    margin: 0 0 15px 0;
    line-height: 1.4;
    font-size: 0.9rem;
}

.mobile-info-popup button {
    background: #4ecdc4;
    border: none;
    color: white;
    padding: 8px 16px;
    border-radius: 8px;
    cursor: pointer;
    font-weight: 600;
}

.mobile-info-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    z-index: 9999;
}

/* Floating Save Button */
.floating-save-btn {
    position: fixed;
    bottom: 30px;
    right: 30px;
    z-index: 1000;
    opacity: 0;
    transform: translateY(100px);
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.floating-save-btn.show {
    opacity: 1;
    transform: translateY(0);
}

.fab-save-btn {
    background: linear-gradient(135deg, var(--success-color), #26d0ce);
    border: none;
    border-radius: 30px;
    padding: 15px 25px;
    color: white;
    font-weight: 600;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 10px;
    box-shadow: 0 8px 25px rgba(78, 205, 196, 0.4);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.fab-save-btn:hover {
    transform: translateY(-3px) scale(1.05);
    box-shadow: 0 12px 35px rgba(78, 205, 196, 0.6);
}

.fab-save-btn:active {
    transform: translateY(-1px) scale(0.98);
}

.fab-icon {
    font-size: 1.3rem;
}

.fab-text {
    font-size: 1rem;
    white-space: nowrap;
}

.fab-counter {
    background: rgba(255, 255, 255, 0.3);
    border-radius: 15px;
    padding: 3px 8px;
    font-size: 0.8rem;
    font-weight: 700;
    min-width: 20px;
    text-align: center;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .enhanced-control-bar {
        margin: 0 -10px 20px -10px;
        padding: 15px;
        border-radius: 15px;
        position: relative;
        top: auto;
    }

    .control-container {
        flex-direction: column;
        gap: 15px;
    }

    .control-left {
        width: 100%;
        justify-content: space-between;
        min-width: auto;
    }

    .control-center {
        width: 100%;
        max-width: none;
    }

    .control-right {
        width: 100%;
        justify-content: space-between;
        flex-wrap: wrap;
    }

    /* Mobile grid sizes - respect dropdown selection */
    .ingredient-grid {
        gap: 8px !important;
        padding-left: 8px !important;
        padding-right: 8px !important;
    }

    /* Mobile grid size classes - smaller heights for mobile */
    .ingredient-grid.size-4 {
        grid-template-columns: repeat(4, 1fr) !important;
    }
    .ingredient-grid.size-6 {
        grid-template-columns: repeat(6, 1fr) !important;
    }
    .ingredient-grid.size-8 {
        grid-template-columns: repeat(8, 1fr) !important;
    }
    .ingredient-grid.size-10 {
        grid-template-columns: repeat(10, 1fr) !important;
    }
    .ingredient-grid.size-12 {
        grid-template-columns: repeat(12, 1fr) !important;
    }

    /* Mobile heights for different grid sizes */
    .ingredient-grid.size-4 .ingredient-item { height: 160px !important; }
    .ingredient-grid.size-6 .ingredient-item { height: 140px !important; }
    .ingredient-grid.size-8 .ingredient-item { height: 120px !important; }
    .ingredient-grid.size-10 .ingredient-item { height: 100px !important; }
    .ingredient-grid.size-12 .ingredient-item { height: 90px !important; }

    .card-content {
        height: 35px;
        padding: 6px;
    }

    .ingredient-name {
        font-size: 0.75rem;
    }

    .action-btn {
        min-width: 35px;
        width: auto;
        height: 35px;
        font-size: 0.9rem;
        padding: 0 8px;
    }

    .action-btn .btn-text {
        font-size: 0.75rem;
    }

    .action-btn .btn-icon {
        font-size: 1rem;
    }

    .control-title {
        font-size: 1.1rem;
    }

    .stat-badge {
        padding: 4px 8px;
        font-size: 0.8rem;
    }
}

/* Portrait mobile - smaller default but still respect grid size */
@media (max-width: 576px) and (orientation: portrait) {
    /* Default to smaller grid on portrait but allow override */
    .ingredient-grid:not(.size-4):not(.size-6):not(.size-8):not(.size-10):not(.size-12) {
        grid-template-columns: repeat(3, 1fr) !important;
    }

    /* Portrait heights for different grid sizes */
    .ingredient-grid.size-4 .ingredient-item { height: 140px !important; }
    .ingredient-grid.size-6 .ingredient-item { height: 120px !important; }
    .ingredient-grid.size-8 .ingredient-item { height: 100px !important; }
    .ingredient-grid.size-10 .ingredient-item { height: 90px !important; }
    .ingredient-grid.size-12 .ingredient-item { height: 80px !important; }

    .card-content {
        height: 30px;
        padding: 4px;
    }

    .ingredient-name {
        font-size: 0.7rem;
    }

    .action-btn {
        min-width: 30px;
        height: 30px;
        padding: 0 6px;
    }

    .action-btn .btn-text {
        font-size: 0.7rem;
    }
}

/* Landscape mobile - allow more columns */
@media (max-width: 768px) and (orientation: landscape) {
    /* Landscape can handle more columns, respect dropdown fully */
    .ingredient-grid.size-4 .ingredient-item { height: 120px !important; }
    .ingredient-grid.size-6 .ingredient-item { height: 100px !important; }
    .ingredient-grid.size-8 .ingredient-item { height: 90px !important; }
    .ingredient-grid.size-10 .ingredient-item { height: 80px !important; }
    .ingredient-grid.size-12 .ingredient-item { height: 70px !important; }
}
</style>

<script>
// Performance Optimized JavaScript with Debouncing and Virtual Scrolling

// Lightweight performance optimization
function initializePerformanceOptimizations() {
    // Enable hardware acceleration
    document.documentElement.style.transform = 'translateZ(0)';

    // Passive scroll listener for better performance
    let scrollTimeout;
    document.addEventListener('scroll', function() {
        clearTimeout(scrollTimeout);
        scrollTimeout = setTimeout(() => {
            // Minimal scroll handling
        }, 16); // ~60fps
    }, { passive: true });
}

// Removed virtual scrolling for better performance

// Debounced search function
const debouncedSearch = debounce(function(searchTerm) {
    performSearch(searchTerm);
}, 300);

// Debounce utility function
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}

// Fallback lazy loading implementation
function initializeFallbackLazyLoading() {
    const lazyImages = document.querySelectorAll('.lazy');

    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.add('loaded');
                    imageObserver.unobserve(img);
                }
            });
        }, {
            threshold: 0.1,
            rootMargin: '50px'
        });

        lazyImages.forEach(img => imageObserver.observe(img));
    } else {
        // Fallback for older browsers
        lazyImages.forEach(img => {
            img.src = img.dataset.src;
            img.classList.add('loaded');
        });
    }
}

// Enhanced JavaScript with Animations and Features

// Ultra-Fast Initialization - No Heavy Libraries
document.addEventListener('DOMContentLoaded', function() {
    // Essential performance setup
    initializePerformanceOptimizations();

    // Initialize native lazy loading
    initializeFallbackLazyLoading();

    // Core functionality only
    initializeIngredientCards();
    initializeFilters();
    initializeSearch();
    updateSelectedCount();
});

// Enhanced ingredient card interactions for simple cards
function initializeIngredientCards() {
    const cards = document.querySelectorAll('.ingredient-card');

    cards.forEach(card => {
        const checkbox = card.querySelector('input[type="checkbox"]');

        // Card click handler
        card.addEventListener('click', function(e) {
            if (e.target.closest('.info-button')) return;

            checkbox.checked = !checkbox.checked;
            updateCardState(card, checkbox.checked);
            updateSelectedCount();

            // Add ripple effect
            createRippleEffect(card, e);
        });

        // Checkbox change handler
        checkbox.addEventListener('change', function() {
            updateCardState(card, this.checked);
            updateSelectedCount();
        });

        // Initialize card state
        updateCardState(card, checkbox.checked);
    });
}

// Update card visual state for simple cards
function updateCardState(card, isSelected) {
    if (isSelected) {
        card.classList.add('selected');
    } else {
        card.classList.remove('selected');
    }
}

// Removed old mobile info functions - using new universal system

// Create ripple effect on click
function createRippleEffect(element, event) {
    const ripple = document.createElement('div');
    const rect = element.getBoundingClientRect();
    const size = Math.max(rect.width, rect.height);
    const x = event.clientX - rect.left - size / 2;
    const y = event.clientY - rect.top - size / 2;

    ripple.style.cssText = `
        position: absolute;
        width: ${size}px;
        height: ${size}px;
        left: ${x}px;
        top: ${y}px;
        background: radial-gradient(circle, rgba(102, 126, 234, 0.3) 0%, transparent 70%);
        border-radius: 50%;
        transform: scale(0);
        animation: ripple 0.6s ease-out;
        pointer-events: none;
        z-index: 1000;
    `;

    element.appendChild(ripple);

    setTimeout(() => {
        ripple.remove();
    }, 600);
}

// Enhanced search functionality
function initializeSearch() {
    const searchInput = document.getElementById('searchInput');
    let searchTimeout;

    searchInput.addEventListener('input', function() {
        clearTimeout(searchTimeout);
        searchTimeout = setTimeout(() => {
            performSearch(this.value);
        }, 300);
    });
}

function performSearch(query) {
    const items = document.querySelectorAll('.ingredient-item');
    const lowerQuery = query.toLowerCase();
    let visibleCount = 0;

    items.forEach((item, index) => {
        const name = item.querySelector('.ingredient-name').textContent.toLowerCase();
        const isVisible = name.includes(lowerQuery);

        if (isVisible) {
            item.style.display = 'block';
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });

    // Update search results indicator
    updateSearchResults(visibleCount, items.length);
}

function updateSearchResults(visible, total) {
    const searchInput = document.getElementById('searchInput');
    if (visible < total && searchInput.value) {
        searchInput.style.borderColor = 'var(--warning-color)';
    } else {
        searchInput.style.borderColor = 'var(--border-color)';
    }
}

// Filter functionality
function initializeFilters() {
    const filterTabs = document.querySelectorAll('.filter-tab');

    filterTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            // Update active tab
            filterTabs.forEach(t => t.classList.remove('active'));
            this.classList.add('active');

            // Apply filter
            const filter = this.dataset.filter;
            applyFilter(filter);
        });
    });
}

function applyFilter(filter) {
    const items = document.querySelectorAll('.ingredient-item');
    let visibleCount = 0;

    items.forEach((item, index) => {
        const category = item.dataset.category;
        const isVisible = filter === 'all' || category === filter;

        if (isVisible) {
            item.style.display = 'block';
            item.style.animationDelay = `${index * 30}ms`;
            visibleCount++;
        } else {
            item.style.display = 'none';
        }
    });

    // Animate filter change
    animateFilterChange();
}

function animateFilterChange() {
    const grid = document.getElementById('ingredient-list');
    grid.style.opacity = '0.7';
    grid.style.transform = 'scale(0.98)';

    setTimeout(() => {
        grid.style.opacity = '1';
        grid.style.transform = 'scale(1)';
    }, 200);
}

// Grid size functionality
function updateGridSize(size) {
    const grid = document.getElementById('ingredient-list');

    // Remove all size classes
    grid.classList.remove('size-4', 'size-6', 'size-8', 'size-10', 'size-12');

    // Add new size class
    grid.classList.add(`size-${size}`);

    // Animate grid change
    grid.style.transform = 'scale(0.95)';
    setTimeout(() => {
        grid.style.transform = 'scale(1)';
    }, 200);
}

// Enhanced action functions for simple cards
function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="ingredients"]');
    let delay = 0;

    checkboxes.forEach((cb, index) => {
        setTimeout(() => {
            if (!cb.checked) {
                cb.checked = true;
                const card = cb.closest('.ingredient-card');
                updateCardState(card, true);

                // Removed animation for performance
            }
        }, delay);
        delay += 20;
    });

    setTimeout(() => {
        updateSelectedCount();
        showToast('✅ Alle ingredienser valgt!', 'success');
    }, delay);
}

function clearAll() {
    const checkboxes = document.querySelectorAll('input[name="ingredients"]');
    let delay = 0;

    checkboxes.forEach((cb, index) => {
        setTimeout(() => {
            if (cb.checked) {
                cb.checked = false;
                const card = cb.closest('.ingredient-card');
                updateCardState(card, false);

                // Removed animation for performance
            }
        }, delay);
        delay += 15;
    });

    setTimeout(() => {
        updateSelectedCount();
        showToast('❌ Alle valg ryddet!', 'info');
    }, delay);
}

function randomSelection() {
    // First clear all
    clearAll();

    setTimeout(() => {
        const checkboxes = Array.from(document.querySelectorAll('input[name="ingredients"]'));
        const randomCount = Math.min(10, Math.floor(checkboxes.length * 0.3));
        const shuffled = checkboxes.sort(() => 0.5 - Math.random());
        const selected = shuffled.slice(0, randomCount);

        selected.forEach((cb, index) => {
            setTimeout(() => {
                cb.checked = true;
                const card = cb.closest('.ingredient-card');
                updateCardState(card, true);

                // Removed animation for performance
            }, index * 100);
        });

        setTimeout(() => {
            updateSelectedCount();
            showToast(`🎲 ${randomCount} tilfældige ingredienser valgt!`, 'success');
        }, selected.length * 100);
    }, 500);
}

// Update selected count with animation
function updateSelectedCount() {
    const checkboxes = document.querySelectorAll('input[name="ingredients"]:checked');
    const countElement = document.getElementById('selected-count');
    const fabCounter = document.getElementById('fabCounter');
    const floatingBtn = document.getElementById('floatingSaveBtn');
    const newCount = checkboxes.length;

    if (countElement) {
        // Animate count change
        countElement.style.transform = 'scale(1.2)';
        countElement.textContent = newCount;

        setTimeout(() => {
            countElement.style.transform = 'scale(1)';
        }, 200);

        // Update badge color based on count
        if (newCount === 0) {
            countElement.style.background = 'var(--text-muted)';
        } else if (newCount < 5) {
            countElement.style.background = 'linear-gradient(135deg, var(--warning-color), #ffa726)';
        } else if (newCount < 15) {
            countElement.style.background = 'linear-gradient(135deg, var(--primary-color), var(--secondary-color))';
        } else {
            countElement.style.background = 'linear-gradient(135deg, var(--success-color), #26d0ce)';
        }
    }

    // Update floating save button
    if (fabCounter) {
        fabCounter.textContent = newCount;
    }

    // Show/hide floating save button
    if (floatingBtn) {
        if (newCount > 0) {
            floatingBtn.style.display = 'block';
            setTimeout(() => {
                floatingBtn.classList.add('show');
            }, 100);
        } else {
            floatingBtn.classList.remove('show');
            setTimeout(() => {
                floatingBtn.style.display = 'none';
            }, 400);
        }
    }
}

// Toast notification system
function showToast(message, type = 'info') {
    const toast = document.createElement('div');
    toast.className = `toast-notification toast-${type}`;
    toast.innerHTML = `
        <div class="toast-content">
            <span class="toast-message">${message}</span>
            <button class="toast-close" onclick="this.parentElement.parentElement.remove()">×</button>
        </div>
    `;

    // Add toast styles if not already present
    if (!document.querySelector('.toast-styles')) {
        const styles = document.createElement('style');
        styles.className = 'toast-styles';
        styles.textContent = `
            .toast-notification {
                position: fixed;
                top: 20px;
                right: 20px;
                background: var(--bg-primary);
                border: 1px solid var(--border-color);
                border-radius: 10px;
                padding: 15px;
                box-shadow: 0 10px 30px var(--shadow-medium);
                z-index: 10000;
                transform: translateX(400px);
                transition: all 0.3s ease;
                max-width: 300px;
            }
            .toast-notification.show {
                transform: translateX(0);
            }
            .toast-content {
                display: flex;
                align-items: center;
                justify-content: space-between;
                gap: 10px;
            }
            .toast-message {
                color: var(--text-primary);
                font-weight: 500;
            }
            .toast-close {
                background: none;
                border: none;
                color: var(--text-secondary);
                font-size: 1.2rem;
                cursor: pointer;
                padding: 0;
                width: 20px;
                height: 20px;
                display: flex;
                align-items: center;
                justify-content: center;
            }
            .toast-success { border-left: 4px solid var(--success-color); }
            .toast-info { border-left: 4px solid var(--primary-color); }
            .toast-warning { border-left: 4px solid var(--warning-color); }
            .toast-error { border-left: 4px solid var(--danger-color); }
        `;
        document.head.appendChild(styles);
    }

    document.body.appendChild(toast);

    setTimeout(() => {
        toast.classList.add('show');
    }, 100);

    setTimeout(() => {
        toast.classList.remove('show');
        setTimeout(() => {
            if (toast.parentElement) {
                toast.remove();
            }
        }, 300);
    }, 3000);
}

// Touch-friendly tooltip system
let activeTooltip = null;

function showIngredientInfo(button, event) {
    event.stopPropagation();

    const card = button.closest('.ingredient-card');
    const description = card.querySelector('.hidden-description').textContent;

    // Remove any existing tooltip
    hideAllTooltips();

    // Create new tooltip
    const tooltip = document.createElement('div');
    tooltip.className = 'custom-tooltip';
    tooltip.textContent = description;

    // Position tooltip
    const rect = button.getBoundingClientRect();
    tooltip.style.cssText = `
        position: fixed;
        left: ${Math.min(rect.left, window.innerWidth - 250)}px;
        top: ${rect.top - 60}px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 14px;
        max-width: 240px;
        z-index: 10000;
        pointer-events: none;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
    `;

    document.body.appendChild(tooltip);
    activeTooltip = tooltip;

    // Auto-hide after 3 seconds on touch devices
    if ('ontouchstart' in window) {
        setTimeout(() => {
            if (activeTooltip === tooltip) {
                hideAllTooltips();
            }
        }, 3000);
    }
}

function showTooltip(button) {
    // Only for mouse hover on desktop
    if ('ontouchstart' in window) return;

    const card = button.closest('.ingredient-card');
    const description = card.querySelector('.hidden-description').textContent;

    hideAllTooltips();

    const tooltip = document.createElement('div');
    tooltip.className = 'custom-tooltip hover-tooltip';
    tooltip.textContent = description;

    const rect = button.getBoundingClientRect();
    tooltip.style.cssText = `
        position: fixed;
        left: ${Math.min(rect.left, window.innerWidth - 250)}px;
        top: ${rect.top - 60}px;
        background: rgba(0, 0, 0, 0.9);
        color: white;
        padding: 8px 12px;
        border-radius: 8px;
        font-size: 14px;
        max-width: 240px;
        z-index: 10000;
        pointer-events: none;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        opacity: 0;
        transition: opacity 0.2s ease;
    `;

    document.body.appendChild(tooltip);
    activeTooltip = tooltip;

    // Fade in
    setTimeout(() => {
        if (tooltip.parentNode) {
            tooltip.style.opacity = '1';
        }
    }, 10);
}

function hideTooltip(button) {
    if ('ontouchstart' in window) return;
    hideAllTooltips();
}

function hideAllTooltips() {
    if (activeTooltip && activeTooltip.parentNode) {
        activeTooltip.parentNode.removeChild(activeTooltip);
    }
    activeTooltip = null;
}

// Hide tooltips when clicking elsewhere
document.addEventListener('click', function(event) {
    if (!event.target.closest('.info-button')) {
        hideAllTooltips();
    }
});

// Removed all animations for maximum performance
</script>
{% endblock %}
