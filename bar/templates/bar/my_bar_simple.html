{% extends 'bar/base.html' %}
{% load static %}

{% block title %}Min Bar - MyBar{% endblock %}

{% block content %}
<div class="container-fluid">
    <!-- Simple Top Control Bar -->
    <div class="top-control-bar">
        <div class="row align-items-center">
            <div class="col-md-3">
                <h4>⚙️ Rediger Min Bar</h4>
            </div>
            <div class="col-md-6">
                <input type="text" class="form-control" placeholder="🔍 Søg ingredienser..." id="searchInput">
            </div>
            <div class="col-md-3 text-end">
                <button onclick="selectAll()" class="btn btn-outline-primary btn-sm me-1">✅ Alle</button>
                <button onclick="clearAll()" class="btn btn-outline-secondary btn-sm me-1">❌ Ryd</button>
                <button type="submit" form="bar-form" class="btn btn-success btn-sm">💾 Gem</button>
            </div>
        </div>
    </div>

    <!-- Ingredient Grid -->
    <form method="post" id="bar-form">
        {% csrf_token %}
        <div class="ingredient-grid" id="ingredient-list">
            {% for ingredient in ingredients %}
            <div class="ingredient-item">
                <label class="ingredient-card {% if ingredient.id in selected_ids %}selected{% endif %}">
                    <input type="checkbox" name="ingredients" value="{{ ingredient.id }}" 
                           {% if ingredient.id in selected_ids %}checked{% endif %}>
                    
                    {% if ingredient.image %}
                        <img src="{{ ingredient.image.url }}" alt="{{ ingredient.name }}" class="ingredient-image">
                    {% else %}
                        <div class="ingredient-image no-image">📦</div>
                    {% endif %}
                    
                    <div class="ingredient-name">{{ ingredient.name }}</div>
                </label>
            </div>
            {% endfor %}
        </div>
    </form>
</div>

<style>
/* Simple, working CSS */
.top-control-bar {
    background: white;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    position: sticky;
    top: 10px;
    z-index: 100;
}

.ingredient-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    gap: 15px;
    padding: 20px;
}

.ingredient-item {
    display: block;
}

.ingredient-card {
    display: block;
    background: white;
    border: 2px solid #ddd;
    border-radius: 15px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    height: 180px;
    position: relative;
}

.ingredient-card:hover {
    border-color: #667eea;
    transform: translateY(-5px);
    box-shadow: 0 10px 25px rgba(0,0,0,0.1);
}

.ingredient-card.selected {
    border-color: #667eea;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
    transform: translateY(-3px);
}

.ingredient-card input[type="checkbox"] {
    display: none;
}

.ingredient-image {
    width: 80px;
    height: 80px;
    object-fit: cover;
    border-radius: 10px;
    margin-bottom: 10px;
    display: block;
    margin-left: auto;
    margin-right: auto;
}

.ingredient-image.no-image {
    background: #f8f9fa;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    color: #6c757d;
}

.ingredient-name {
    font-weight: 600;
    font-size: 0.9rem;
    color: #333;
    margin-top: 10px;
}

/* Mobile responsive */
@media (max-width: 768px) {
    .top-control-bar {
        position: relative;
        top: auto;
    }
    
    .top-control-bar .row {
        flex-direction: column;
        gap: 10px;
    }
    
    .ingredient-grid {
        grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
        gap: 10px;
        padding: 10px;
    }
    
    .ingredient-card {
        height: 150px;
        padding: 10px;
    }
    
    .ingredient-image {
        width: 60px;
        height: 60px;
    }
    
    .ingredient-name {
        font-size: 0.8rem;
    }
}
</style>

<script>
// Simple JavaScript
function selectAll() {
    const checkboxes = document.querySelectorAll('input[name="ingredients"]');
    checkboxes.forEach(cb => {
        cb.checked = true;
        cb.closest('.ingredient-card').classList.add('selected');
    });
}

function clearAll() {
    const checkboxes = document.querySelectorAll('input[name="ingredients"]');
    checkboxes.forEach(cb => {
        cb.checked = false;
        cb.closest('.ingredient-card').classList.remove('selected');
    });
}

// Search functionality
document.getElementById('searchInput').addEventListener('input', function() {
    const query = this.value.toLowerCase();
    const items = document.querySelectorAll('.ingredient-item');
    
    items.forEach(item => {
        const name = item.querySelector('.ingredient-name').textContent.toLowerCase();
        if (name.includes(query)) {
            item.style.display = 'block';
        } else {
            item.style.display = 'none';
        }
    });
});

// Handle checkbox changes
document.querySelectorAll('input[name="ingredients"]').forEach(cb => {
    cb.addEventListener('change', function() {
        const card = this.closest('.ingredient-card');
        if (this.checked) {
            card.classList.add('selected');
        } else {
            card.classList.remove('selected');
        }
    });
});
</script>
{% endblock %}
