{% extends 'bar/base.html' %}
{% load static %}

{% block title %}Bartender Dashboard{% endblock %}

{% block extra_css %}
<style>
    .dashboard-header {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        color: white;
        padding: 30px 0;
        margin-bottom: 30px;
        border-radius: 0 0 20px 20px;
    }
    
    .status-card {
        background: white;
        border-radius: 15px;
        padding: 25px;
        margin-bottom: 20px;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
        border-left: 5px solid;
    }
    
    .status-card.pending {
        border-left-color: #ff9800;
    }
    
    .status-card.in-progress {
        border-left-color: #2196f3;
    }
    
    .status-card.completed {
        border-left-color: #4caf50;
    }
    
    .request-item {
        background: #f8f9fa;
        border-radius: 10px;
        padding: 20px;
        margin-bottom: 15px;
        border: 1px solid #e0e0e0;
        transition: all 0.3s ease;
    }
    
    .request-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
    
    .drink-name {
        font-size: 1.3rem;
        font-weight: 600;
        color: #333;
        margin-bottom: 10px;
    }
    
    .guest-info {
        color: #666;
        margin-bottom: 10px;
    }
    
    .request-time {
        font-size: 0.9rem;
        color: #999;
    }
    
    .status-badge {
        display: inline-block;
        padding: 5px 12px;
        border-radius: 20px;
        font-size: 0.85rem;
        font-weight: 600;
        text-transform: uppercase;
    }
    
    .status-badge.pending {
        background: #fff3e0;
        color: #f57c00;
    }
    
    .status-badge.in-progress {
        background: #e3f2fd;
        color: #1976d2;
    }
    
    .status-badge.completed {
        background: #e8f5e8;
        color: #2e7d32;
    }
    
    .action-btn {
        padding: 8px 16px;
        border: none;
        border-radius: 8px;
        font-size: 0.9rem;
        font-weight: 600;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-right: 10px;
    }
    
    .btn-start {
        background: #2196f3;
        color: white;
    }
    
    .btn-complete {
        background: #4caf50;
        color: white;
    }
    
    .btn-cancel {
        background: #f44336;
        color: white;
    }
    
    .action-btn:hover {
        transform: scale(1.05);
        opacity: 0.9;
    }
    
    .empty-state {
        text-align: center;
        padding: 40px;
        color: #999;
    }
    
    .refresh-btn {
        position: fixed;
        bottom: 30px;
        right: 30px;
        background: #667eea;
        color: white;
        border: none;
        border-radius: 50%;
        width: 60px;
        height: 60px;
        font-size: 1.5rem;
        cursor: pointer;
        box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3);
        transition: all 0.3s ease;
    }
    
    .refresh-btn:hover {
        transform: scale(1.1);
        box-shadow: 0 6px 20px rgba(102, 126, 234, 0.4);
    }
    
    .tablet-info {
        font-size: 0.9rem;
        color: #666;
        margin-bottom: 5px;
    }

    .drink-details {
        background: #f8f9fa;
        padding: 15px;
        border-radius: 8px;
        border-left: 3px solid #667eea;
    }

    .ingredient-list {
        list-style: none;
        padding: 0;
        margin: 0 0 15px 0;
    }

    .ingredient-list li {
        padding: 3px 0;
        font-size: 0.9rem;
        border-bottom: 1px solid #eee;
    }

    .ingredient-list li:last-child {
        border-bottom: none;
    }

    .instructions {
        font-size: 0.9rem;
        color: #555;
        background: white;
        padding: 10px;
        border-radius: 5px;
        border: 1px solid #ddd;
    }

    .drink-details h6 {
        color: #667eea;
        font-weight: 600;
        margin-bottom: 8px;
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
        .drink-details {
            margin-top: 15px;
        }

        .request-item .row {
            flex-direction: column;
        }
    }
</style>
{% endblock %}

{% block content %}
<div class="dashboard-header">
    <div class="container">
        <h1><i class="fas fa-tachometer-alt me-3"></i>Bartender Dashboard</h1>
        <p class="lead mb-0">Håndter drink requests fra tablet profiler</p>
    </div>
</div>

<div class="container">
    <!-- Statistics Cards -->
    <div class="row mb-4">
        <div class="col-md-4">
            <div class="status-card pending">
                <h3><i class="fas fa-clock me-2"></i>{{ pending_requests.count }}</h3>
                <p class="mb-0">Afventer</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="status-card in-progress">
                <h3><i class="fas fa-spinner me-2"></i>{{ in_progress_requests.count }}</h3>
                <p class="mb-0">I gang</p>
            </div>
        </div>
        <div class="col-md-4">
            <div class="status-card completed">
                <h3><i class="fas fa-check-circle me-2"></i>{{ completed_requests.count }}</h3>
                <p class="mb-0">Færdige (seneste)</p>
            </div>
        </div>
    </div>

    <!-- Active Profiles Info -->
    {% if profiles %}
    <div class="row mb-4">
        <div class="col-12">
            <div class="alert alert-info">
                <h5><i class="fas fa-info-circle me-2"></i>Aktive Profiler</h5>
                {% for profile in profiles %}
                    <span class="badge bg-primary me-2">{{ profile.name }} ({{ profile.requester_user.username }})</span>
                {% endfor %}
            </div>
        </div>
    </div>
    {% endif %}
    
    <!-- Pending Requests -->
    <div class="row">
        <div class="col-12">
            <h2><i class="fas fa-clock text-warning me-2"></i>Afventende Requests</h2>
            {% if pending_requests %}
                {% for request in pending_requests %}
                <div class="request-item" data-request-id="{{ request.id }}">
                    <div class="row">
                        <div class="col-md-6">
                            <div class="drink-name">🍹 {{ request.drink.name }}</div>
                            <div class="guest-info">
                                {% if request.guest_name %}
                                    <i class="fas fa-user me-1"></i>{{ request.guest_name }}
                                {% endif %}
                                {% if request.table_number %}
                                    <i class="fas fa-table ms-3 me-1"></i>{{ request.table_number }}
                                {% endif %}
                            </div>
                            <div class="tablet-info">
                                <i class="fas fa-user me-1"></i>{{ request.requester.username }} ({{ request.profile.name }})
                            </div>
                            {% if request.special_notes %}
                                <div class="text-muted mt-2">
                                    <i class="fas fa-sticky-note me-1"></i>{{ request.special_notes }}
                                </div>
                            {% endif %}
                        </div>
                        <div class="col-md-6">
                            <!-- Ingredienser og fremgangsmåde -->
                            <div class="drink-details">
                                <h6><i class="fas fa-list me-1"></i>Ingredienser:</h6>
                                <ul class="ingredient-list">
                                    {% for drink_ingredient in request.drink.drink_ingredients.all %}
                                        <li>
                                            {% if drink_ingredient.amount %}
                                                {{ drink_ingredient.amount }}{{ drink_ingredient.unit }}
                                            {% endif %}
                                            {{ drink_ingredient.ingredient.name }}
                                            {% if not drink_ingredient.is_required %}
                                                <small class="text-muted">(valgfri)</small>
                                            {% endif %}
                                        </li>
                                    {% endfor %}
                                    {% for group_relation in request.drink.drink_ingredient_groups.all %}
                                        <li>
                                            <strong>{{ group_relation.ingredient_group.name }}:</strong>
                                            {% for ingredient in group_relation.ingredient_group.ingredients.all %}
                                                {{ ingredient.name }}{% if not forloop.last %}, {% endif %}
                                            {% endfor %}
                                            {% if not group_relation.is_required %}
                                                <small class="text-muted">(valgfri)</small>
                                            {% endif %}
                                        </li>
                                    {% endfor %}
                                </ul>

                                <h6><i class="fas fa-clipboard-list me-1"></i>Fremgangsmåde:</h6>
                                <div class="instructions">
                                    {% if request.drink.recipe %}
                                        {{ request.drink.recipe|safe }}
                                    {% else %}
                                        <em>Ingen fremgangsmåde tilgængelig for denne drink.</em>
                                    {% endif %}
                                </div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="request-time">
                                <i class="fas fa-clock me-1"></i>{{ request.requested_at|date:"H:i" }}
                            </div>
                            <span class="status-badge pending">⏳ Afventer</span>
                        </div>
                        <div class="col-md-3 text-end">
                            <button class="action-btn btn-start" onclick="updateStatus({{ request.id }}, 'in_progress')">
                                <i class="fas fa-play me-1"></i>Start
                            </button>
                            <button class="action-btn btn-cancel" onclick="updateStatus({{ request.id }}, 'cancelled')">
                                <i class="fas fa-times me-1"></i>Annuller
                            </button>
                        </div>
                    </div>
                </div>
                {% endfor %}
            {% else %}
                <div class="empty-state">
                    <i class="fas fa-check-circle fa-3x mb-3"></i>
                    <h4>Ingen afventende requests</h4>
                    <p>Alle requests er håndteret!</p>
                </div>
            {% endif %}
        </div>
    </div>
    
    <!-- In Progress Requests -->
    {% if in_progress_requests %}
    <div class="row mt-5">
        <div class="col-12">
            <h2><i class="fas fa-spinner text-primary me-2"></i>I Gang</h2>
            {% for request in in_progress_requests %}
            <div class="request-item" data-request-id="{{ request.id }}">
                <div class="row">
                    <div class="col-md-6">
                        <div class="drink-name">🍹 {{ request.drink.name }}</div>
                        <div class="guest-info">
                            {% if request.guest_name %}
                                <i class="fas fa-user me-1"></i>{{ request.guest_name }}
                            {% endif %}
                            {% if request.table_number %}
                                <i class="fas fa-table ms-3 me-1"></i>{{ request.table_number }}
                            {% endif %}
                        </div>
                        <div class="tablet-info">
                            <i class="fas fa-user me-1"></i>{{ request.requester.username }} ({{ request.profile.name }})
                        </div>
                    </div>
                    <div class="col-md-6">
                        <!-- Ingredienser og fremgangsmåde -->
                        <div class="drink-details">
                            <h6><i class="fas fa-list me-1"></i>Ingredienser:</h6>
                            <ul class="ingredient-list">
                                {% for drink_ingredient in request.drink.drink_ingredients.all %}
                                    <li>
                                        {% if drink_ingredient.amount %}
                                            {{ drink_ingredient.amount }}{{ drink_ingredient.unit }}
                                        {% endif %}
                                        {{ drink_ingredient.ingredient.name }}
                                        {% if not drink_ingredient.is_required %}
                                            <small class="text-muted">(valgfri)</small>
                                        {% endif %}
                                    </li>
                                {% endfor %}
                                {% for group_relation in request.drink.drink_ingredient_groups.all %}
                                    <li>
                                        <strong>{{ group_relation.ingredient_group.name }}:</strong>
                                        {% for ingredient in group_relation.ingredient_group.ingredients.all %}
                                            {{ ingredient.name }}{% if not forloop.last %}, {% endif %}
                                        {% endfor %}
                                        {% if not group_relation.is_required %}
                                            <small class="text-muted">(valgfri)</small>
                                        {% endif %}
                                    </li>
                                {% endfor %}
                            </ul>

                            <h6><i class="fas fa-clipboard-list me-1"></i>Fremgangsmåde:</h6>
                            <div class="instructions">
                                {% if request.drink.recipe %}
                                    {{ request.drink.recipe|safe }}
                                {% else %}
                                    <em>Ingen fremgangsmåde tilgængelig for denne drink.</em>
                                {% endif %}
                            </div>
                        </div>
                    </div>
                    <div class="col-md-3">
                        <div class="request-time">
                            <i class="fas fa-play me-1"></i>{{ request.started_at|date:"H:i" }}
                        </div>
                        <span class="status-badge in-progress">🔄 I gang</span>
                    </div>
                    <div class="col-md-3 text-end">
                        <button class="action-btn btn-complete" onclick="updateStatus({{ request.id }}, 'completed')">
                            <i class="fas fa-check me-1"></i>Færdig
                        </button>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
    
    <!-- Completed Requests -->
    {% if completed_requests %}
    <div class="row mt-5">
        <div class="col-12">
            <h2><i class="fas fa-check-circle text-success me-2"></i>Seneste Færdige</h2>
            {% for request in completed_requests %}
            <div class="request-item">
                <div class="row align-items-center">
                    <div class="col-md-8">
                        <div class="drink-name">🍹 {{ request.drink.name }}</div>
                        <div class="guest-info">
                            {% if request.guest_name %}
                                <i class="fas fa-user me-1"></i>{{ request.guest_name }}
                            {% endif %}
                            {% if request.table_number %}
                                <i class="fas fa-table ms-3 me-1"></i>{{ request.table_number }}
                            {% endif %}
                        </div>
                        <div class="tablet-info">
                            <i class="fas fa-user me-1"></i>{{ request.requester.username }} ({{ request.profile.name }})
                        </div>
                    </div>
                    <div class="col-md-4 text-end">
                        <div class="request-time">
                            <i class="fas fa-check me-1"></i>{{ request.completed_at|date:"H:i" }}
                        </div>
                        <span class="status-badge completed">✅ Færdig</span>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
    </div>
    {% endif %}
</div>

<!-- Refresh Button -->
<button class="refresh-btn" onclick="location.reload()" title="Opdater">
    <i class="fas fa-sync-alt"></i>
</button>

<script>
function updateStatus(requestId, newStatus) {
    fetch('{% url "bar:update_request_status" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': '{{ csrf_token }}'
        },
        body: JSON.stringify({
            request_id: requestId,
            status: newStatus
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Reload page to show updated status
            location.reload();
        } else {
            alert('Fejl: ' + data.error);
        }
    })
    .catch(error => {
        console.error('Error:', error);
        alert('Der opstod en fejl. Prøv igen.');
    });
}

// Live updates every 10 seconds (instead of full page reload)
let lastUpdateTime = Date.now();

function checkForUpdates() {
    fetch('{% url "bar:bartender_dashboard" %}?ajax=1&last_update=' + lastUpdateTime)
    .then(response => response.json())
    .then(data => {
        if (data.has_updates) {
            // Soft reload - just update the content
            location.reload();
        }
        lastUpdateTime = Date.now();
    })
    .catch(error => {
        console.log('Update check failed:', error);
    });
}

// Check for updates every 10 seconds
setInterval(checkForUpdates, 10000);
</script>
{% endblock %}
