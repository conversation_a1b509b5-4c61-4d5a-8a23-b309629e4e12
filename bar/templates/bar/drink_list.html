{% extends "bar/base.html" %}
{% load static %}

{% block title %}Mine Drinks{% endblock %}

{% block content %}
<!-- <PERSON><PERSON> Splash Screen - DISABLED TO FIX BLURRING ISSUE -->
<!--
<div id="pwa-splash" class="pwa-splash">
    <div class="splash-content">
        <img src="{% static 'bar/icons/icon-192x192.png' %}" alt="MyBar" class="splash-logo">
        <h1 class="splash-title">MyBar</h1>
        <div class="splash-loader">
            <div class="loader-bar"></div>
        </div>
        <p class="splash-text">Indlæser drinks...</p>
    </div>
</div>
-->

<!-- Essential Performance Only -->
<script src="https://unpkg.com/vanilla-lazyload@17.8.5/dist/lazyload.min.js" defer></script>
<style>
    /* ===== CLEAN CSS RESET - OVERSKRIVER ALT ===== */

    /* FORCE OVERFLOW FIX */
    * {
        box-sizing: border-box !important;
    }

    body, html {
        overflow-x: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
    }

    .container, .full-width-wrapper {
        overflow-x: hidden !important;
        width: 100% !important;
        max-width: 100% !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
    }

    /* FLIP CARD SYSTEM - HOVER TO SHOW INGREDIENTS */
    .simple-card {
        background: transparent !important;
        border-radius: 15px !important;
        overflow: hidden !important;
        cursor: pointer !important;
        position: relative !important;
        width: 100% !important;
        height: auto !important;
        min-height: 200px !important;
        perspective: 1000px !important;
        transform: translateZ(0) !important;
    }

    .card-content {
        position: relative !important;
        width: 100% !important;
        height: 100% !important;
        text-align: center !important;
        transition: transform 0.6s ease !important;
        transform-style: preserve-3d !important;
        -webkit-transform-style: preserve-3d !important;
        min-height: 200px !important;
        perspective: 1000px !important;
    }

    .simple-card:hover .card-content {
        transform: rotateY(180deg) !important;
        -webkit-transform: rotateY(180deg) !important;
    }

    /* Add blur effect to front side image when flipped */
    .simple-card:hover .card-front .drink-card-img {
        filter: blur(3px) !important;
        transition: filter 0.6s ease !important;
    }

    /* DEBUG STYLES - ONLY FOR SUPERUSERS */

    .card-front, .card-back {
        position: absolute !important;
        width: 100% !important;
        height: 100% !important;
        -webkit-backface-visibility: hidden !important;
        backface-visibility: hidden !important;
        border-radius: 15px !important;
        display: flex !important;
        flex-direction: column !important;
        min-height: 200px !important;
    }

    .card-front {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
        box-shadow: 0 4px 16px rgba(31, 38, 135, 0.2) !important;
        border: 1px solid rgba(255, 255, 255, 0.18) !important;
        position: relative !important;
        overflow: hidden !important;
    }

    .card-back {
        background: linear-gradient(135deg, #764ba2 0%, #667eea 100%) !important;
        color: white !important;
        transform: rotateY(180deg) !important;
        padding: 20px !important;
        justify-content: flex-start !important;
        box-shadow: 0 4px 16px rgba(31, 38, 135, 0.2) !important;
        border: 1px solid rgba(255, 255, 255, 0.18) !important;
        z-index: 2 !important; /* Lower z-index to work with backface-visibility */
        opacity: 1 !important;
        overflow-y: auto !important;
        backdrop-filter: blur(10px) !important;
        -webkit-backface-visibility: hidden !important;
        backface-visibility: hidden !important;
    }

    /* Ensure flip cards work with new grid system */
    .simple-card {
        width: 100% !important;
        height: 100% !important;
    }

    .card-content {
        width: 100% !important;
        height: 100% !important;
    }



    /* Badge Styling - Positioned above text overlay */
    .substitution-badge, .missing-badge {
        position: absolute;
        bottom: 55px; /* Position above the text overlay */
        left: 8px;
        right: 8px;
        background: rgba(255, 193, 7, 0.95);
        color: #333;
        padding: 4px 8px;
        border-radius: 8px;
        font-size: 0.7rem;
        font-weight: 600;
        text-align: center;
        z-index: 15; /* Above image but below flip card back */
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.4);
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
        box-shadow: 0 3px 6px rgba(0,0,0,0.4);
    }

    .missing-badge {
        background: rgba(244, 67, 54, 0.95);
        color: white;
    }

    /* FLIP CARD BACK SIDE STYLING - MAXIMUM VISIBILITY */
    .drink-name-back {
        font-size: 1.2rem !important;
        margin-bottom: 12px !important;
        color: #ffffff !important;
        font-weight: 700 !important;
        text-shadow: 0 2px 6px rgba(0,0,0,0.8) !important;
        z-index: 150 !important;
        position: relative !important;
        text-align: center !important;
        background: rgba(255,255,255,0.1) !important;
        padding: 8px !important;
        border-radius: 10px !important;
        border: 1px solid rgba(255,255,255,0.3) !important;
    }

    .ingredients-title {
        font-size: 1rem !important;
        font-weight: bold !important;
        margin-bottom: 10px !important;
        color: #ffffff !important;
        text-shadow: 0 2px 4px rgba(0,0,0,0.8) !important;
        z-index: 150 !important;
        position: relative !important;
        text-align: center !important;
        background: rgba(255,255,255,0.1) !important;
        padding: 6px !important;
        border-radius: 8px !important;
    }

    .ingredients-list-back {
        list-style: none !important;
        padding: 0 !important;
        margin: 0 !important;
        z-index: 150 !important;
        position: relative !important;
        max-height: 120px !important;
        overflow-y: auto !important;
    }

    .ingredients-list-back li {
        font-size: 0.85rem !important;
        margin-bottom: 6px !important;
        color: #ffffff !important;
        background: rgba(255,255,255,0.25) !important;
        padding: 6px 12px !important;
        border-radius: 15px !important;
        backdrop-filter: blur(10px) !important;
        border: 1px solid rgba(255,255,255,0.4) !important;
        text-shadow: 0 1px 3px rgba(0,0,0,0.6) !important;
        z-index: 150 !important;
        position: relative !important;
        font-weight: 500 !important;
        box-shadow: 0 2px 4px rgba(0,0,0,0.3) !important;
    }

    .missing-ingredient {
        color: #ffeb3b !important;
        font-weight: bold;
    }

    /* Modal missing ingredient highlighting */
    .modal-content .missing-ingredient-highlight {
        background: linear-gradient(45deg, #ffd700, #ffed4e) !important;
        color: #333 !important;
        padding: 3px 8px !important;
        border-radius: 6px !important;
        font-weight: bold !important;
        text-shadow: none !important;
        box-shadow: 0 2px 6px rgba(255,215,0,0.4) !important;
        border: 1px solid rgba(255,215,0,0.6) !important;
        animation: pulse-glow 2s infinite !important;
    }

    @keyframes pulse-glow {
        0%, 100% { box-shadow: 0 2px 6px rgba(255,215,0,0.4); }
        50% { box-shadow: 0 4px 12px rgba(255,215,0,0.8); }
    }

    /* Mobile optimizations completed above */

    /* SIMPLE GRID SYSTEM - FRESH REBUILD */
    .drink-grid {
        display: grid;
        gap: 15px;
        padding: 10px;
        width: 100%;
    }

    /* Default 8 columns */
    .drink-grid {
        grid-template-columns: repeat(8, 1fr);
    }

    /* Grid size classes */
    .grid-4 .drink-grid { grid-template-columns: repeat(4, 1fr); }
    .grid-6 .drink-grid { grid-template-columns: repeat(6, 1fr); }
    .grid-8 .drink-grid { grid-template-columns: repeat(8, 1fr); }
    .grid-10 .drink-grid { grid-template-columns: repeat(10, 1fr); }
    .grid-12 .drink-grid { grid-template-columns: repeat(12, 1fr); }

    .drink-item {
        width: 100%;
        height: 220px; /* Reduced from 250px for better proportions */
    }

    /* Mobile responsive */
    @media (max-width: 768px) {
        .grid-4 .drink-grid { grid-template-columns: repeat(2, 1fr); }
        .grid-6 .drink-grid { grid-template-columns: repeat(3, 1fr); }
        .grid-8 .drink-grid { grid-template-columns: repeat(4, 1fr); }
        .grid-10 .drink-grid { grid-template-columns: repeat(4, 1fr); }
        .grid-12 .drink-grid { grid-template-columns: repeat(4, 1fr); }

        .drink-item {
            height: 180px; /* Reduced for mobile */
        }

        /* Mobile image adjustments - still fill entire card */
        .drink-card-img {
            height: 100% !important; /* Fill entire mobile card height */
        }

        /* Mobile text scaling - override desktop responsive rules */
        .card-front h3 {
            font-size: 0.9rem !important;
            padding: 12px 8px 8px 8px !important;
        }

        .grid-4 .card-front h3,
        .grid-6 .card-front h3,
        .grid-8 .card-front h3,
        .grid-10 .card-front h3,
        .grid-12 .card-front h3 {
            font-size: 0.9rem !important;
            padding: 12px 8px 8px 8px !important;
        }
    }

    /* MOBILE RESPONSIVE - REMOVED FOR REBUILD */

    /* DESKTOP RESPONSIVE - REMOVED FOR REBUILD */

    /* PREVENT OVERFLOW AND ENSURE PROPER SIZING */
    .full-width-wrapper {
        width: 100% !important;
        max-width: 100% !important;
        overflow-x: hidden !important;
    }

    .container {
        width: 100% !important;
        max-width: 100% !important;
        padding: 0 10px !important;
        margin: 0 !important;
        box-sizing: border-box !important;
    }

    /* ENSURE TEXT DOESN'T BREAK LAYOUT */
    .simple-card h3 {
        font-size: 0.85rem !important;
        margin: 8px 0 0 0 !important;
        padding: 0 8px 8px 8px !important;
        text-align: center !important;
        word-wrap: break-word !important;
        overflow-wrap: break-word !important;
        hyphens: auto !important;
        line-height: 1.2 !important;
        color: white !important;
        flex-shrink: 0 !important;
        background: rgba(0, 0, 0, 0.85) !important;
        border-radius: 0 0 15px 15px !important;
    }

    /* COMPLEX GRID CSS - REMOVED FOR REBUILD */

        /* DRINK CARD IMAGE STYLING - FILL ENTIRE CARD WITH CUSTOM POSITIONING */
        .drink-card-img {
            width: 100% !important;
            height: 100% !important; /* Fill entire card height */
            object-fit: cover !important;
            border-radius: 15px !important; /* Match card border radius */
            display: block !important;
            transition: transform 0.3s ease, filter 0.6s ease !important;
            position: absolute !important;
            top: 0 !important;
            left: 0 !important;
            z-index: 1 !important;
            opacity: 1 !important; /* CRITICAL FIX: Always show images */
        }

        /* Dynamic object-position will be set via inline styles based on admin settings */

        /* PERFORMANCE OPTIMIZED LAZY LOADING */
        .drink-card-img {
            will-change: transform, filter;
            transform: translateZ(0); /* Force GPU acceleration */
        }

        .drink-card-img[loading="lazy"] {
            opacity: 1 !important; /* FIXED: Always show images */
            transition: opacity 0.2s ease;
        }

        .drink-card-img[loading="lazy"].loaded {
            opacity: 1;
        }

        /* Reduce animation complexity on mobile for better performance */
        @media (max-width: 768px) {
            .drink-card-img {
                transition: transform 0.2s ease !important;
            }

            .simple-card .card-content {
                transition: transform 0.4s ease !important;
            }
        }

        /* CARD FRONT LAYOUT - IMAGE + TEXT OVERLAY */
        .card-front {
            padding: 0 !important; /* Remove padding to allow full image */
        }

        .card-front h3 {
            position: absolute !important;
            bottom: 0 !important;
            left: 0 !important;
            right: 0 !important;
            margin: 0 !important;
            padding: 20px 10px 12px 10px !important;
            background: linear-gradient(transparent, rgba(0,0,0,0.85)) !important;
            color: white !important;
            font-size: 1rem !important;
            font-weight: 600 !important;
            text-align: center !important;
            text-shadow: 0 2px 4px rgba(0,0,0,0.8) !important;
            z-index: 5 !important;
            line-height: 1.2 !important;
            border-radius: 0 0 15px 15px !important;
        }

        /* RESPONSIVE TEXT SCALING FOR DIFFERENT GRID SIZES */
        .grid-4 .card-front h3 {
            font-size: 1.4rem !important;
            padding: 20px 15px 15px 15px !important;
        }

        .grid-6 .card-front h3 {
            font-size: 1.2rem !important;
            padding: 18px 12px 12px 12px !important;
        }

        .grid-8 .card-front h3 {
            font-size: 1rem !important;
            padding: 15px 10px 10px 10px !important;
        }

        .grid-10 .card-front h3 {
            font-size: 0.9rem !important;
            padding: 12px 8px 8px 8px !important;
        }

        .grid-12 .card-front h3 {
            font-size: 0.8rem !important;
            padding: 10px 6px 6px 6px !important;
        }

    /* CONTROLS PANEL - FRESH REBUILD */
    .controls-panel {
        background: rgba(255, 255, 255, 0.1);
        backdrop-filter: blur(10px);
        border-radius: 12px;
        padding: 16px; /* Reduced from 20px */
        margin: 16px auto; /* Reduced from 20px */
        max-width: 1200px;
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .controls-container {
        display: flex;
        flex-direction: column;
        gap: 15px;
    }

    .controls-title {
        color: white;
        margin: 0;
        font-size: 1.3rem; /* Reduced from 1.5rem */
        font-weight: 600;
    }

    .controls-row {
        display: flex;
        gap: 15px;
        flex-wrap: wrap;
        align-items: center;
    }

    .control-input, .control-select {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 10px;
        padding: 10px 15px;
        color: #333;
        font-weight: 500;
        min-width: 150px;
    }

    .control-input:focus, .control-select:focus {
        outline: none;
        background: rgba(255, 255, 255, 0.3);
        border-color: #667eea;
        box-shadow: 0 0 0 2px rgba(102, 126, 234, 0.3);
    }

    .control-input::placeholder {
        color: rgba(51, 51, 51, 0.7);
    }

    @media (max-width: 768px) {
        .controls-row {
            flex-direction: column;
            align-items: stretch;
        }

        .control-input, .control-select {
            width: 100%;
            min-width: unset;
        }
    }

    /* FLOATING ACTION BUTTON */
    .fab-container {
        position: fixed;
        bottom: 30px;
        right: 30px;
        z-index: 1000;
    }

    .fab-main {
        width: 60px;
        height: 60px;
        border-radius: 50%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border: none;
        box-shadow: 0 8px 25px rgba(102, 126, 234, 0.4);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.5rem;
        color: white;
    }

    .fab-main:hover {
        transform: scale(1.1);
        box-shadow: 0 12px 35px rgba(102, 126, 234, 0.6);
    }

    .fab-menu {
        position: absolute;
        bottom: 80px;
        right: 0;
        display: flex;
        flex-direction: column;
        gap: 15px;
        opacity: 0;
        transform: translateY(20px);
        transition: all 0.3s ease;
        pointer-events: none;
    }

    .fab-menu.active {
        opacity: 1;
        transform: translateY(0);
        pointer-events: all;
    }

    /* PWA Splash Screen */
    .pwa-splash {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        display: flex;
        align-items: center;
        justify-content: center;
        z-index: 9999;
        opacity: 1;
        transition: opacity 0.5s ease;
    }

    .pwa-splash.hidden {
        opacity: 0;
        pointer-events: none;
    }

    .splash-content {
        text-align: center;
        color: white;
    }

    .splash-logo {
        width: 120px;
        height: 120px;
        border-radius: 20px;
        margin-bottom: 20px;
        animation: bounceIn 1s ease;
    }

    .splash-title {
        font-size: 2.5rem;
        font-weight: 700;
        margin-bottom: 30px;
        animation: fadeInUp 1s ease 0.3s both;
    }

    .splash-loader {
        width: 200px;
        height: 4px;
        background: rgba(255, 255, 255, 0.3);
        border-radius: 2px;
        margin: 0 auto 20px;
        overflow: hidden;
        animation: fadeInUp 1s ease 0.6s both;
    }

    .loader-bar {
        width: 0%;
        height: 100%;
        background: white;
        border-radius: 2px;
        animation: loadProgress 2s ease-in-out;
    }

    .splash-text {
        font-size: 1.1rem;
        opacity: 0.9;
        animation: fadeInUp 1s ease 0.9s both;
    }

    @keyframes bounceIn {
        0% { transform: scale(0.3); opacity: 0; }
        50% { transform: scale(1.05); }
        70% { transform: scale(0.9); }
        100% { transform: scale(1); opacity: 1; }
    }

    @keyframes fadeInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }

    @keyframes loadProgress {
        0% { width: 0%; }
        50% { width: 70%; }
        100% { width: 100%; }
    }

    /* Notification animations */
    @keyframes slideIn {
        from {
            transform: translateX(100%);
            opacity: 0;
        }
        to {
            transform: translateX(0);
            opacity: 1;
        }
    }

    @keyframes slideOut {
        from {
            transform: translateX(0);
            opacity: 1;
        }
        to {
            transform: translateX(100%);
            opacity: 0;
        }
    }

    .fab-item {
        width: 50px;
        height: 50px;
        border-radius: 50%;
        background: rgba(255, 255, 255, 0.9);
        border: none;
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        cursor: pointer;
        transition: all 0.3s ease;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 1.2rem;
        backdrop-filter: blur(10px);
    }

    .fab-item:hover {
        transform: scale(1.1);
        background: rgba(255, 255, 255, 1);
    }

    /* SEARCH STYLING */
    .search-container input {
        background: rgba(255, 255, 255, 0.2);
        border: 1px solid rgba(255, 255, 255, 0.3);
        border-radius: 25px;
        color: white;
        padding: 8px 15px;
    }

    .search-container input::placeholder {
        color: rgba(255, 255, 255, 0.7);
    }

    .search-container input:focus {
        background: rgba(255, 255, 255, 0.3);
        border-color: #667eea;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        outline: none;
    }

    /* LOADING ANIMATION */
    .loading-skeleton {
        background: linear-gradient(90deg, rgba(255,255,255,0.1) 25%, rgba(255,255,255,0.2) 50%, rgba(255,255,255,0.1) 75%);
        background-size: 200% 100%;
        animation: loading 1.5s infinite;
        border-radius: 15px;
        height: 120px;
        margin-bottom: 10px;
    }

    @keyframes loading {
        0% { background-position: 200% 0; }
        100% { background-position: -200% 0; }
    }

    /* SIMPLE CARD HOVER EFFECTS */
    .drink-item:hover .simple-card {
        transform: translateY(-3px);
        box-shadow: 0 8px 24px rgba(102, 126, 234, 0.3);
    }

    /* FAVORITE ICON STYLING */
    .favorite-icon {
        position: absolute;
        top: 10px;
        right: 10px;
        z-index: 20; /* Above everything including flip card back */
        font-size: 1.5rem;
        cursor: pointer;
        transition: all 0.3s ease;
        background: rgba(0, 0, 0, 0.6);
        border-radius: 50%;
        width: 40px;
        height: 40px;
        display: flex;
        align-items: center;
        justify-content: center;
        backdrop-filter: blur(10px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .favorite-icon:hover {
        transform: scale(1.2);
        background: rgba(0, 0, 0, 0.7);
    }

    .favorite-icon.favorited {
        background: rgba(255, 0, 0, 0.2);
        border: 2px solid rgba(255, 0, 0, 0.5);
    }

    /* DIFFICULTY INDICATORS */
    .difficulty-indicator {
        position: absolute;
        top: 10px;
        left: 10px;
        z-index: 10;
    }

    .difficulty {
        background: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 4px 8px;
        border-radius: 12px;
        font-size: 0.7rem;
        font-weight: 600;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.2);
    }

    .difficulty.easy {
        background: rgba(76, 175, 80, 0.8);
        border-color: rgba(76, 175, 80, 0.5);
    }

    .difficulty.medium {
        background: rgba(255, 193, 7, 0.8);
        border-color: rgba(255, 193, 7, 0.5);
    }

    .difficulty.hard {
        background: rgba(244, 67, 54, 0.8);
        border-color: rgba(244, 67, 54, 0.5);
    }

    /* STAR RATING SYSTEM */
    .star-rating {
        display: flex;
        gap: 5px;
        margin: 10px 0;
    }

    .star {
        font-size: 1.5rem;
        cursor: pointer;
        transition: all 0.2s ease;
        filter: grayscale(100%);
    }

    .star:hover,
    .star.active {
        filter: grayscale(0%);
        transform: scale(1.2);
    }

    .star.active {
        text-shadow: 0 0 10px #ffd700;
    }

    /* COMMENTS SECTION */
    .comments-section {
        border-top: 1px solid rgba(255,255,255,0.2);
        padding-top: 20px;
    }

    .comment-item {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 15px;
        margin-bottom: 10px;
        backdrop-filter: blur(5px);
    }

    .comment-author {
        font-weight: bold;
        color: #ffd700;
        margin-bottom: 5px;
    }

    .comment-date {
        font-size: 0.8rem;
        color: rgba(255,255,255,0.6);
        margin-bottom: 10px;
    }

    .comment-text {
        color: rgba(255,255,255,0.9);
        line-height: 1.5;
    }

    /* MODERN HOMEPAGE DESIGN - FULL WIDTH LIKE INGREDIENTS LIST */
    body {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
        min-height: 100vh;
        position: relative;
        overflow-x: hidden;
        margin: 0;
        padding: 0;
    }

    body.modal-open {
        overflow: hidden;
    }

    /* Particles background */
    #particles-js {
        position: fixed;
        width: 100%;
        height: 100%;
        top: 0;
        left: 0;
        z-index: -1;
    }

    /* Hero Section - MINIMAL DESIGN */
    .hero-section {
        background: rgba(255, 255, 255, 0.05);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 25px;
        margin: 15px 20px;
        text-align: center;
        border: 1px solid rgba(255, 255, 255, 0.1);
        box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
    }

    .hero-title {
        font-size: 2.2rem;
        font-weight: 600;
        color: white;
        margin-bottom: 15px;
        text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
    }

    .hero-subtitle {
        font-size: 1rem;
        color: rgba(255, 255, 255, 0.8);
        margin-bottom: 20px;
        font-weight: 400;
    }

    .stats-container {
        display: flex;
        justify-content: center;
        gap: 20px;
        margin-top: 20px;
        flex-wrap: wrap;
    }

    .stat-item {
        background: rgba(255, 255, 255, 0.1);
        border-radius: 12px;
        padding: 12px 20px;
        backdrop-filter: blur(5px);
        border: 1px solid rgba(255, 255, 255, 0.15);
        text-align: center;
        min-width: 80px;
        transition: all 0.3s ease;
    }

    .stat-item:hover {
        background: rgba(255, 255, 255, 0.15);
        transform: translateY(-2px);
    }

    .stat-number {
        font-size: 1.8rem;
        font-weight: 600;
        color: #fff;
        display: block;
    }

    .stat-label {
        font-size: 0.75rem;
        color: rgba(255, 255, 255, 0.7);
        text-transform: uppercase;
        letter-spacing: 0.5px;
    }

    /* FULL-WIDTH WRAPPER LIKE INGREDIENTS LIST */
    .full-width-wrapper {
        width: 100%;
        min-height: 100vh;
        position: relative;
        z-index: 1;
    }

    .container {
        padding: 0 !important;
        margin: 0 !important;
        width: 100% !important;
        max-width: 100% !important;
        position: relative;
        z-index: 1;
        overflow-x: hidden;
        box-sizing: border-box;
    }

    .full-width-wrapper {
        width: 100%;
        max-width: 100%;
        overflow-x: hidden;
        box-sizing: border-box;
    }

    /* Section headers styling */
    .section-header {
        max-width: 1200px;
        margin-left: auto;
        margin-right: auto;
        padding-left: 20px;
        padding-right: 20px;
    }

    /* Mobile adjustments */
    @media (max-width: 576px) {
        .hero-section {
            margin-left: 10px;
            margin-right: 10px;
        }

        .control-panel {
            margin-left: 10px;
            margin-right: 10px;
        }

        .section-header {
            padding-left: 10px;
            padding-right: 10px;
        }

        /* Mobile layout optimizations */

        body {
            overflow-x: hidden !important;
        }
    }
    .modal {
        display: none;
        position: fixed;
        z-index: 1000;
        left: 0;
        top: 0;
        width: 100vw;
        height: 100vh;
        background-color: rgba(0,0,0,0.8);
        backdrop-filter: blur(5px);
        overflow-y: auto;
        overflow-x: hidden;
    }

    .modal.show {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        padding: 20px !important;
        box-sizing: border-box !important;
        min-height: 100vh !important;
    }

    /* Prevent body scroll when modal is open - PRESERVE SCROLL POSITION */
    body.modal-open {
        overflow: hidden !important;
        position: fixed !important;
        width: 100% !important;
        top: var(--scroll-y, 0) !important;
        left: 0 !important;
    }

    .modal-content {
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        padding: 30px;
        border: none;
        width: 100%;
        max-width: 700px;
        max-height: 90vh;
        border-radius: 20px;
        position: relative;
        box-shadow: 0 20px 60px rgba(0,0,0,0.3);
        color: white;
        overflow-y: auto;
        margin: 0 auto;
        transform: scale(0.7);
        opacity: 0;
        transition: all 0.3s ease;
    }

    .modal.show .modal-content {
        transform: scale(1);
        opacity: 1;
    }

.modal.open .modal-content {
    opacity: 1; /* Bliver synlig */
    transform: translateY(0); /* Rykker til den korrekte position */
}

    .modal-content h2, .modal-content h3 {
        color: white;
        text-shadow: 0 2px 4px rgba(0,0,0,0.3);
    }

    .modal-content ul {
        background: rgba(255,255,255,0.1);
        border-radius: 10px;
        padding: 15px;
        backdrop-filter: blur(10px);
    }

    .modal-content li {
        color: rgba(255,255,255,0.9);
        margin-bottom: 8px;
        padding: 5px 0;
        border-bottom: 1px solid rgba(255,255,255,0.1);
    }

    .modal-content div {
        color: rgba(255,255,255,0.9);
        line-height: 1.6;
    }

    /* Mobile modal optimizations */
    @media (max-width: 768px) {
        .modal {
            padding: 10px;
        }

        .modal-content {
            max-height: 95vh;
            padding: 20px;
        }

        .modal-content .row {
            flex-direction: column;
        }

        .modal-content .col-md-4,
        .modal-content .col-md-8 {
            width: 100%;
            max-width: 100%;
        }
    }
    .close {
        color: #aaa;
        position: absolute;
        top: 10px;
        right: 25px;
        font-size: 35px;
        font-weight: bold;
    }
    .close:hover,
    .close:focus {
        color: black;
        text-decoration: none;
        cursor: pointer;
    }
    .no-drinks-message {
        text-align: center;
        padding: 50px;
    }
    .no-drinks-message .btn {
        margin-top: 20px;
        padding: 10px 20px;
        background-color: #007bff;
        color: white;
        text-decoration: none;
        border-radius: 5px;
    }
</style>

<!-- Particles Background - REMOVED FOR BETTER PERFORMANCE -->
<!-- <div id="particles-js"></div> -->

<!-- Full-width wrapper like ingredients list -->
<div class="full-width-wrapper">
<div class="container grid-8" id="mainContainer">
    <!-- HERO SECTION -->
    <div class="hero-section">
        <h1 class="hero-title">🍹 Mine Drinks</h1>
        <p class="hero-subtitle">Opdag fantastiske drinks du kan lave med dine ingredienser</p>

        <div class="stats-container">
            <div class="stat-item">
                <span class="stat-number">{{ complete_drinks|length }}</span>
                <span class="stat-label">Klar til at lave</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ substitute_drinks|length }}</span>
                <span class="stat-label">Med erstatninger</span>
            </div>
            <div class="stat-item">
                <span class="stat-number">{{ almost_drinks|length }}</span>
                <span class="stat-label">Næsten klar</span>
            </div>
        </div>
    </div>

    <!-- CONTROLS SECTION - COMPLETELY REBUILT -->
    <div class="controls-panel">
        <div class="controls-container">
            <h2 class="controls-title">🎛️ Kontroller</h2>
            <div class="controls-row">
                <!-- Search Field -->
                <input type="text" id="searchField" class="control-input" placeholder="🔍 Søg drinks...">

                <!-- Difficulty Filter -->
                <select id="difficultyFilter" class="control-select">
                    <option value="all">⚡ Alle Sværhedsgrader</option>
                    <option value="easy">🟢 Let (1-3 ingredienser)</option>
                    <option value="medium">🟡 Medium (4-5 ingredienser)</option>
                    <option value="hard">🔴 Svær (6+ ingredienser)</option>
                </select>

                <!-- Type Filter -->
                <select id="typeFilter" class="control-select">
                    <option value="all">🍹 Alle Typer</option>
                    <option value="cocktail">🍸 Cocktails</option>
                    <option value="shot">🥃 Shots</option>
                    <option value="drink">🥤 Drinks</option>
                    <option value="nonalcoholic">🧃 Alkoholfri</option>
                </select>

                <!-- Grid Layout -->
                <select id="gridLayout" class="control-select">
                    <option value="4">4 per række</option>
                    <option value="6">6 per række</option>
                    <option value="8" selected>8 per række</option>
                    <option value="10">10 per række</option>
                    <option value="12">12 per række</option>
                </select>
            </div>
        </div>
    </div>

    {% if complete_drinks or substitute_drinks or almost_drinks %}
        {% if complete_drinks %}
            <div class="section-header" id="complete-section">
                <h2 style="color: white; font-size: 2.2rem; font-weight: 700; margin-bottom: 25px;">
                    ✅ Du kan lave disse drinks
                    <span style="background: rgba(76, 175, 80, 0.2); color: #4caf50; padding: 4px 12px; border-radius: 18px; font-size: 0.9rem; margin-left: 12px;">
                        {{ complete_drinks|length }} drinks
                    </span>
                </h2>
            </div>
            <div class="drink-grid" id="complete-grid">
                {% for drink in complete_drinks %}
                <div class="drink-item" data-drink-type="{{ drink.drink_type|default:'drink' }}" data-drink-id="{{ drink.id }}">
                    <div class="simple-card" onclick="openModal(this)">
                        <div class="card-content">
                            <!-- FRONT SIDE -->
                            <div class="card-front">
                                <!-- Difficulty Indicator -->
                                <div class="difficulty-indicator">
                                    {% if drink.drink_ingredients.count <= 3 %}
                                        <span class="difficulty easy">🟢 Let</span>
                                    {% elif drink.drink_ingredients.count <= 5 %}
                                        <span class="difficulty medium">🟡 Medium</span>
                                    {% else %}
                                        <span class="difficulty hard">🔴 Svær</span>
                                    {% endif %}
                                </div>

                                <!-- Favorite Icon -->
                                <div class="favorite-icon {% if drink.favorite %}favorited{% endif %}"
                                     onclick="event.stopPropagation(); toggleDrinkFavorite({{ drink.id }}, this)">
                                    {% if drink.favorite %}❤️{% else %}🤍{% endif %}
                                </div>

                                <img src="{% if drink.image %}{{ drink.image.url }}{% else %}data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNjY3ZWVhIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSI0MCI+8J+NuTwvdGV4dD4KPC9zdmc+{% endif %}" class="drink-card-img" loading="lazy" style="object-position: {{ drink.image_position_x|default:50 }}% {{ drink.image_position_y|default:50 }}%;">
                                <h3>{{ drink.name }}</h3>
                            </div>

                            <!-- BACK SIDE - INGREDIENTS -->
                            <div class="card-back">
                                <h4 class="drink-name-back">{{ drink.name }}</h4>
                                <p class="ingredients-title">Ingredienser:</p>
                                <ul class="ingredients-list-back">
                                    {% for ing in drink.drink_ingredients.all|slice:":5" %}
                                        <li>{{ ing.amount }} {{ ing.unit }} {{ ing.ingredient.name }}</li>
                                    {% endfor %}
                                    {% if drink.drink_ingredients.all|length > 5 %}
                                        <li>... og {{ drink.drink_ingredients.all|length|add:"-5" }} mere</li>
                                    {% endif %}
                                </ul>
                                <p style="font-size: 0.8rem; margin-top: 10px; opacity: 0.8;">Klik for opskrift</p>
                            </div>
                        </div>

                        <!-- Hidden data for modal -->
                        <div class="drink-card-info" style="display:none;">
                            <div class="ingredients-list">{% for ing in drink.drink_ingredients.all %}<li>{{ ing.amount }} {{ ing.unit }} {{ ing.ingredient.name }}</li>{% endfor %}</div>
                            <div class="instructions-text">{{ drink.recipe|striptags|safe|linebreaksbr }}</div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% endif %}

        {% if substitute_drinks %}
            <div class="section-header" id="substitute-section" style="margin-top: 60px;">
                <h2 style="color: white; font-size: 2.2rem; font-weight: 700; margin-bottom: 25px;">
                    🔄 Næsten fuld plade
                    <span style="background: rgba(255, 193, 7, 0.2); color: #ffc107; padding: 4px 12px; border-radius: 18px; font-size: 0.9rem; margin-left: 12px;">
                        {{ substitute_drinks|length }} drinks
                    </span>
                </h2>
            </div>
            <div class="drink-grid" id="substitute-grid">
                {% for drink in substitute_drinks %}
                <div class="drink-item" data-drink-type="{{ drink.drink_type|default:'drink' }}" data-drink-id="{{ drink.id }}">
                    <div class="simple-card" onclick="openModal(this)">
                        <div class="card-content">
                            <!-- FRONT SIDE -->
                            <div class="card-front">
                                <!-- Difficulty Indicator -->
                                <div class="difficulty-indicator">
                                    {% if drink.drink_ingredients.count <= 3 %}
                                        <span class="difficulty easy">🟢 Let</span>
                                    {% elif drink.drink_ingredients.count <= 5 %}
                                        <span class="difficulty medium">🟡 Medium</span>
                                    {% else %}
                                        <span class="difficulty hard">🔴 Svær</span>
                                    {% endif %}
                                </div>

                                <!-- Favorite Icon -->
                                <div class="favorite-icon {% if drink.favorite %}favorited{% endif %}"
                                     onclick="event.stopPropagation(); toggleDrinkFavorite({{ drink.id }}, this)">
                                    {% if drink.favorite %}❤️{% else %}🤍{% endif %}
                                </div>

                                <img src="{% if drink.image %}{{ drink.image.url }}{% else %}data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNjY3ZWVhIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSI0MCI+8J+NuTwvdGV4dD4KPC9zdmc+{% endif %}" class="drink-card-img" loading="lazy" style="object-position: {{ drink.image_position_x|default:50 }}% {{ drink.image_position_y|default:50 }}%;">
                                <h3>{{ drink.name }}</h3>

                                <!-- Substitution Badge -->
                                <div class="substitution-badge">🔄 Med erstatninger</div>
                            </div>

                            <!-- BACK SIDE - INGREDIENTS -->
                            <div class="card-back">
                                <h4 class="drink-name-back">{{ drink.name }}</h4>
                                <p class="ingredients-title">Ingredienser:</p>
                                <ul class="ingredients-list-back">
                                    {% for ing in drink.drink_ingredients.all|slice:":5" %}
                                        <li>{{ ing.amount }} {{ ing.unit }} {{ ing.ingredient.name }}</li>
                                    {% endfor %}
                                    {% if drink.drink_ingredients.all|length > 5 %}
                                        <li>... og {{ drink.drink_ingredients.all|length|add:"-5" }} mere</li>
                                    {% endif %}
                                </ul>
                                <p style="font-size: 0.7rem; margin-top: 8px; opacity: 0.8; color: #ffc107;">🔄 Med erstatninger</p>
                                <p style="font-size: 0.8rem; margin-top: 5px; opacity: 0.8;">Klik for opskrift</p>
                            </div>
                        </div>

                        <!-- Hidden data for modal -->
                        <div class="drink-card-info" style="display:none;">
                            <div class="ingredients-list">{% for ing in drink.drink_ingredients.all %}<li>{{ ing.amount }} {{ ing.unit }} {{ ing.ingredient.name }}</li>{% endfor %}</div>
                            <div class="instructions-text">{{ drink.recipe|striptags|safe|linebreaksbr }}</div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% endif %}

        {% if almost_drinks %}
            <div class="section-header" id="almost-section" style="margin-top: 60px;">
                <h2 style="color: white; font-size: 2.2rem; font-weight: 700; margin-bottom: 25px;">
                    ⏳ Lige ved at være der - kun 1 ingrediens mangler
                    <span style="background: rgba(244, 67, 54, 0.2); color: #f44336; padding: 4px 12px; border-radius: 18px; font-size: 0.9rem; margin-left: 12px;">
                        {{ almost_drinks|length }} drinks
                    </span>
                </h2>
            </div>
            <div class="drink-grid" id="almost-grid">
                {% for drink in almost_drinks %}
                <div class="drink-item" data-drink-type="{{ drink.drink_type|default:'drink' }}" data-drink-id="{{ drink.id }}">
                    <div class="simple-card" onclick="openModal(this)">
                        <div class="card-content">
                            <!-- FRONT SIDE -->
                            <div class="card-front">
                                <!-- Difficulty Indicator -->
                                <div class="difficulty-indicator">
                                    {% if drink.drink_ingredients.count <= 3 %}
                                        <span class="difficulty easy">🟢 Let</span>
                                    {% elif drink.drink_ingredients.count <= 5 %}
                                        <span class="difficulty medium">🟡 Medium</span>
                                    {% else %}
                                        <span class="difficulty hard">🔴 Svær</span>
                                    {% endif %}
                                </div>

                                <!-- Favorite Icon -->
                                <div class="favorite-icon {% if drink.favorite %}favorited{% endif %}"
                                     onclick="event.stopPropagation(); toggleDrinkFavorite({{ drink.id }}, this)">
                                    {% if drink.favorite %}❤️{% else %}🤍{% endif %}
                                </div>

                                <img src="{% if drink.image %}{{ drink.image.url }}{% else %}data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNjY3ZWVhIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSI0MCI+8J+NuTwvdGV4dD4KPC9zdmc+{% endif %}" class="drink-card-img" loading="lazy" style="object-position: {{ drink.image_position_x|default:50 }}% {{ drink.image_position_y|default:50 }}%;">
                                <h3>{{ drink.name }}</h3>

                                <!-- Missing Ingredient Badge -->
                                <div class="missing-badge">⏳ Mangler: {{ drink.missing_ingredient }}</div>
                            </div>

                            <!-- BACK SIDE - INGREDIENTS -->
                            <div class="card-back">
                                <h4 class="drink-name-back">{{ drink.name }}</h4>
                                <p class="ingredients-title">Ingredienser:</p>
                                <ul class="ingredients-list-back">
                                    {% for ing in drink.drink_ingredients.all|slice:":5" %}
                                        <li class="{% if ing.ingredient.name == drink.missing_ingredient %}missing-ingredient{% endif %}">{{ ing.amount }} {{ ing.unit }} {{ ing.ingredient.name }}</li>
                                    {% endfor %}
                                    {% if drink.drink_ingredients.all|length > 5 %}
                                        <li>... og {{ drink.drink_ingredients.all|length|add:"-5" }} mere</li>
                                    {% endif %}
                                </ul>
                                <p style="font-size: 0.7rem; margin-top: 8px; opacity: 0.8; color: #f44336;">⏳ Mangler: {{ drink.missing_ingredient }}</p>
                                <p style="font-size: 0.8rem; margin-top: 5px; opacity: 0.8;">Klik for opskrift</p>
                            </div>
                        </div>

                        <!-- Hidden data for modal -->
                        <div class="drink-card-info" style="display:none;">
                            <div class="ingredients-list">{% for ing in drink.drink_ingredients.all %}<li>{{ ing.amount }} {{ ing.unit }} {{ ing.ingredient.name }}</li>{% endfor %}</div>
                            <div class="instructions-text">{{ drink.recipe|striptags|safe|linebreaksbr }}</div>
                        </div>
                    </div>
                </div>
                {% endfor %}
            </div>
        {% endif %}

    {% else %}
        <div class="no-drinks-message">
            <h2>Du har endnu ikke ingredienser til at lave nogle drinks</h2>
            <p>Tilføj de ingredienser du har i din bar for at se, hvilke drinks du kan lave.</p>
            <a href="#" class="btn">Gå til Min Bar</a>
        </div>
    {% endif %}
</div>
</div> <!-- Close full-width-wrapper -->

<!-- Floating Action Button -->
<div class="fab-container">
    <div class="fab-menu" id="fabMenu">
        <button class="fab-item" onclick="scrollToTop()" title="Scroll til top">
            ⬆️
        </button>
        <button class="fab-item" onclick="toggleRandomDrink()" title="Tilfældig drink">
            🎲
        </button>
        <button class="fab-item" onclick="toggleFavorites()" title="Kun favoritter">
            ❤️
        </button>
        <button class="fab-item" onclick="window.location.href='{% url 'bar:my_bar' %}'" title="Min Bar">
            🏠
        </button>
    </div>
    <button class="fab-main" id="fabMain" onclick="toggleFabMenu()">
        ✨
    </button>
</div>

<!-- The Modal -->
<div id="drinkModal" class="modal">
  <div class="modal-content">
    <span class="close" onclick="closeModal()">&times;</span>

    <div class="row">
        <div class="col-md-4">
            <img id="modalDrinkImage" src="" alt="Drink billede" style="width: 100%; max-height: 300px; object-fit: cover; border-radius: 15px;">

            <!-- Rating Section -->
            <div class="rating-section mt-3">
                <h4>Din bedømmelse:</h4>
                <div class="star-rating" id="userRating">
                    <span class="star" data-rating="1">⭐</span>
                    <span class="star" data-rating="2">⭐</span>
                    <span class="star" data-rating="3">⭐</span>
                    <span class="star" data-rating="4">⭐</span>
                    <span class="star" data-rating="5">⭐</span>
                </div>
                <div class="average-rating mt-2">
                    <small>Gennemsnit: <span id="averageRating">0.0</span> ⭐</small>
                </div>
            </div>
        </div>

        <div class="col-md-8">
            <h2 id="modalDrinkName" style="text-align: center; margin-bottom: 20px;"></h2>

            {% if can_request_drinks %}
            <!-- Drink Request Section -->
            <div class="request-section mb-4" style="background: rgba(255,255,255,0.1); padding: 20px; border-radius: 15px;">
                <h3 style="color: #fff; margin-bottom: 15px;">🍹 Bestil denne drink</h3>
                <div class="row">
                    <div class="col-md-6">
                        <input type="text" id="requestGuestName" class="form-control mb-2" placeholder="Dit navn (valgfrit)" style="background: rgba(255,255,255,0.2); border: none; color: white;">
                    </div>
                    <div class="col-md-6">
                        <input type="text" id="requestTableNumber" class="form-control mb-2" placeholder="Bordnummer (valgfrit)" style="background: rgba(255,255,255,0.2); border: none; color: white;">
                    </div>
                </div>
                <textarea id="requestSpecialNotes" class="form-control mb-3" rows="2" placeholder="Særlige ønsker..." style="background: rgba(255,255,255,0.2); border: none; color: white;"></textarea>
                <button class="btn btn-success btn-lg w-100" onclick="submitDrinkRequest()" style="background: #28a745; border: none; font-weight: 600;">
                    <i class="fas fa-paper-plane me-2"></i>Send bestilling til bartender
                </button>
            </div>
            {% endif %}

            <h3>Ingredienser:</h3>
            <ul id="modalDrinkIngredients"></ul>
            <h3>Fremgangsmåde:</h3>
            <div id="modalDrinkInstructions"></div>
        </div>
    </div>

    <!-- Comments Section -->
    <div class="comments-section mt-4">
        <h3>Anmeldelser:</h3>

        <!-- Add Comment Form -->
        <div class="add-comment mb-3">
            <textarea id="commentText" class="form-control" rows="3" placeholder="Skriv din anmeldelse..." style="background: rgba(255,255,255,0.2); border: 1px solid rgba(255,255,255,0.3); color: white;"></textarea>
            <button id="submitComment" class="btn btn-light mt-2">Tilføj anmeldelse</button>
        </div>

        <!-- Comments List -->
        <div id="commentsList">
            <!-- Comments will be loaded here -->
        </div>
    </div>
  </div>
</div>

<script>
let currentDrinkId = null;

function openModal(cardElement) {
    try {
        const modal = document.getElementById('drinkModal');
        const modalImg = document.getElementById('modalDrinkImage');
        const modalTitle = document.getElementById('modalDrinkName');
        const modalInstructions = document.getElementById('modalDrinkInstructions');
        const modalIngredients = document.getElementById('modalDrinkIngredients');

        // --- Data Extraction with Safety Checks - Updated for flip card structure ---
        const titleElement = cardElement.querySelector('.card-front h3') || cardElement.querySelector('h3');
        const title = titleElement ? titleElement.innerText : "Ukendt Drink";

        const imageElement = cardElement.querySelector('.card-front .drink-card-img') || cardElement.querySelector('.drink-card-img');
        const imageSrc = imageElement ? imageElement.src : "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNjY3ZWVhIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSI0MCI+8J+NuTwvdGV4dD4KPC9zdmc+";

        const ingredientsElement = cardElement.querySelector('.drink-card-info .ingredients-list');
        const ingredientsHTML = ingredientsElement ? ingredientsElement.innerHTML : '<li>Ingredienser ikke tilgængelige.</li>';

        const instructionsElement = cardElement.querySelector('.drink-card-info .instructions-text');
        let instructionsHTML = instructionsElement ? instructionsElement.innerHTML : 'Fremgangsmåde ikke tilgængelig.';

        // Decode HTML entities
        instructionsHTML = decodeHtmlEntities(instructionsHTML);

        // Get drink ID from parent element
        const drinkItem = cardElement.closest('.drink-item');
        currentDrinkId = drinkItem ? drinkItem.getAttribute('data-drink-id') : null;

        // --- Populate Modal ---
        modalTitle.innerText = title;
        modalImg.src = imageSrc;

        // Check if this is from "almost drinks" section and highlight missing ingredient
        const isAlmostDrink = cardElement.closest('#almost-grid') !== null;
        const missingBadge = cardElement.querySelector('.missing-badge');
        let missingIngredient = null;

        if (isAlmostDrink && missingBadge) {
            const badgeText = missingBadge.innerText;
            const match = badgeText.match(/Mangler:\s*(.+)/);
            if (match) {
                missingIngredient = match[1].trim();
            }
        }

        // Highlight missing ingredient in the ingredients list
        if (missingIngredient && ingredientsHTML) {
            const highlightedHTML = ingredientsHTML.replace(
                new RegExp(`(${missingIngredient})`, 'gi'),
                '<span style="background: linear-gradient(45deg, #ffd700, #ffed4e); color: #333; padding: 2px 6px; border-radius: 4px; font-weight: bold; text-shadow: none; box-shadow: 0 2px 4px rgba(255,215,0,0.3);">$1 ⚠️</span>'
            );
            modalIngredients.innerHTML = highlightedHTML;
        } else {
            modalIngredients.innerHTML = ingredientsHTML;
        }

        modalInstructions.innerHTML = instructionsHTML;

        // Load ratings and comments
        loadDrinkRating(currentDrinkId);
        loadDrinkComments(currentDrinkId);

        // --- Display Modal ---
        // Save current scroll position
        const scrollY = window.scrollY;
        document.documentElement.style.setProperty('--scroll-y', `-${scrollY}px`);

        modal.style.display = 'flex';
        setTimeout(() => {
            modal.classList.add('show');
        }, 10);
        document.body.classList.add('modal-open');
    } catch (error) {
        console.error('Fejl ved åbning af modal:', error);
        alert('Der opstod en fejl ved visning af drinken. Se konsollen for detaljer.');
    }
}

// Star rating functionality
function initStarRating() {
    const stars = document.querySelectorAll('.star');
    stars.forEach((star, index) => {
        star.addEventListener('click', () => {
            const rating = index + 1;
            setUserRating(rating);
            if (currentDrinkId) {
                submitRating(currentDrinkId, rating);
            }
        });

        star.addEventListener('mouseenter', () => {
            highlightStars(index + 1);
        });
    });

    document.querySelector('.star-rating').addEventListener('mouseleave', () => {
        const currentRating = getCurrentUserRating();
        highlightStars(currentRating);
    });
}

function highlightStars(rating) {
    const stars = document.querySelectorAll('.star');
    stars.forEach((star, index) => {
        if (index < rating) {
            star.classList.add('active');
        } else {
            star.classList.remove('active');
        }
    });
}

function setUserRating(rating) {
    highlightStars(rating);
    // Store in localStorage temporarily
    localStorage.setItem(`rating_${currentDrinkId}`, rating);
}

function getCurrentUserRating() {
    return parseInt(localStorage.getItem(`rating_${currentDrinkId}`)) || 0;
}

function loadDrinkRating(drinkId) {
    if (!drinkId) return;

    // Load user rating from server
    fetch(`/user-ratings/`)
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            const userRating = data.ratings.find(r => r.drink_id == drinkId);
            if (userRating) {
                highlightStars(userRating.rating);
                localStorage.setItem(`rating_${drinkId}`, userRating.rating);
            }
        }
    })
    .catch(error => {
        console.error('❌ Error loading user rating:', error);
        // Fallback to localStorage
        const userRating = getCurrentUserRating();
        highlightStars(userRating);
    });

    // Load average rating (placeholder for now)
    document.getElementById('averageRating').textContent = '4.2';
}

function submitRating(drinkId, rating) {
    // Send rating to server
    fetch('/api/rate-drink/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            drink_id: drinkId,
            rating: rating
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log(`✅ Rating ${rating} saved for drink ${drinkId}`);
            // Update average rating display
            if (data.average_rating) {
                document.getElementById('averageRating').textContent = data.average_rating;
            }
        } else {
            console.error('❌ Failed to save rating:', data.error);
        }
    })
    .catch(error => {
        console.error('❌ Error submitting rating:', error);
        // Fallback to localStorage
        localStorage.setItem(`rating_${drinkId}`, rating);
    });
}

function loadDrinkComments(drinkId) {
    if (!drinkId) return;

    // Load comments from server
    fetch(`/get-comments/${drinkId}/`)
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            displayComments(data.comments);
        } else {
            console.error('❌ Failed to load comments:', data.error);
            // Fallback to localStorage
            const comments = JSON.parse(localStorage.getItem(`comments_${drinkId}`)) || [];
            displayComments(comments);
        }
    })
    .catch(error => {
        console.error('❌ Error loading comments:', error);
        // Fallback to localStorage
        const comments = JSON.parse(localStorage.getItem(`comments_${drinkId}`)) || [];
        displayComments(comments);
    });
}

function displayComments(comments) {
    const commentsList = document.getElementById('commentsList');
    if (!comments || comments.length === 0) {
        commentsList.innerHTML = '<p style="color: rgba(255,255,255,0.6);">Ingen anmeldelser endnu. Vær den første!</p>';
        return;
    }

    commentsList.innerHTML = comments.map(comment => `
        <div class="comment-item">
            <div class="comment-author">${comment.author || comment.user}</div>
            <div class="comment-date">${comment.date || comment.created_at}</div>
            <div class="comment-text">${comment.text}</div>
        </div>
    `).join('');
}

function submitComment() {
    const commentText = document.getElementById('commentText').value.trim();
    if (!commentText || !currentDrinkId) return;

    // Send comment to server
    fetch('/api/add-comment/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            drink_id: currentDrinkId,
            text: commentText
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            console.log('✅ Comment saved successfully');
            // Refresh comments
            loadDrinkComments(currentDrinkId);
            // Clear form
            document.getElementById('commentText').value = '';
        } else {
            console.error('❌ Failed to save comment:', data.error);
        }
    })
    .catch(error => {
        console.error('❌ Error submitting comment:', error);
        // Fallback to localStorage
        const comment = {
            author: 'Du',
            date: new Date().toLocaleDateString('da-DK'),
            text: commentText
        };
        const comments = JSON.parse(localStorage.getItem(`comments_${currentDrinkId}`)) || [];
        comments.unshift(comment);
        localStorage.setItem(`comments_${currentDrinkId}`, JSON.stringify(comments));
        displayComments(comments);
        document.getElementById('commentText').value = '';
    });
}

// Helper function to decode HTML entities
function decodeHtmlEntities(text) {
    const textarea = document.createElement('textarea');
    textarea.innerHTML = text;
    return textarea.value;
}

// Helper function to get CSRF token
function getCookie(name) {
    let cookieValue = null;
    if (document.cookie && document.cookie !== '') {
        const cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            const cookie = cookies[i].trim();
            if (cookie.substring(0, name.length + 1) === (name + '=')) {
                cookieValue = decodeURIComponent(cookie.substring(name.length + 1));
                break;
            }
        }
    }
    return cookieValue;
}

function closeModal() {
    const modal = document.getElementById('drinkModal');
    modal.classList.remove('show');

    // Restore scroll position
    const scrollY = document.documentElement.style.getPropertyValue('--scroll-y');
    document.body.classList.remove('modal-open');
    document.documentElement.style.removeProperty('--scroll-y');

    setTimeout(() => {
        modal.style.display = 'none';
        if (scrollY) {
            window.scrollTo(0, parseInt(scrollY || '0') * -1);
        }
    }, 300);
}

// Drink Request Function
function submitDrinkRequest() {
    if (!currentDrinkId) {
        alert('Ingen drink valgt');
        return;
    }

    const requestData = {
        drink_id: currentDrinkId,
        guest_name: document.getElementById('requestGuestName').value,
        table_number: document.getElementById('requestTableNumber').value,
        special_notes: document.getElementById('requestSpecialNotes').value
    };

    fetch('{% url "bar:request_drink" %}', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify(requestData)
    })
    .then(response => response.json())
    .then(data => {
        if (data.success) {
            // Show success message
            showNotification('✅ ' + data.message, 'success');

            // Clear form
            document.getElementById('requestGuestName').value = '';
            document.getElementById('requestTableNumber').value = '';
            document.getElementById('requestSpecialNotes').value = '';

            // Close modal
            closeModal();

            // Start checking for updates
            startRequestStatusCheck();
        } else {
            showNotification('❌ Fejl: ' + data.error, 'error');
        }
    })
    .catch(error => {
        console.error('Error:', error);
        showNotification('Der opstod en fejl. Prøv igen.', 'error');
    });
}

// Notification system with sound
function playNotificationSound(type) {
    try {
        // Create audio context for better browser support
        const audioContext = new (window.AudioContext || window.webkitAudioContext)();

        if (type === 'success') {
            // Success sound - pleasant chime
            playTone(audioContext, 523.25, 0.1); // C5
            setTimeout(() => playTone(audioContext, 659.25, 0.1), 100); // E5
            setTimeout(() => playTone(audioContext, 783.99, 0.2), 200); // G5
        } else if (type === 'info') {
            // Info sound - single tone
            playTone(audioContext, 440, 0.2); // A4
        } else if (type === 'error') {
            // Error sound - lower tone
            playTone(audioContext, 220, 0.3); // A3
        }
    } catch (error) {
        console.log('Audio not supported:', error);
    }
}

function playTone(audioContext, frequency, duration) {
    const oscillator = audioContext.createOscillator();
    const gainNode = audioContext.createGain();

    oscillator.connect(gainNode);
    gainNode.connect(audioContext.destination);

    oscillator.frequency.value = frequency;
    oscillator.type = 'sine';

    gainNode.gain.setValueAtTime(0.3, audioContext.currentTime);
    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + duration);

    oscillator.start(audioContext.currentTime);
    oscillator.stop(audioContext.currentTime + duration);
}

function showNotification(message, type = 'info') {
    // Play sound for important notifications
    if (type === 'success' || type === 'error') {
        playNotificationSound(type);
    }

    // Remove existing notifications
    const existing = document.querySelectorAll('.drink-notification');
    existing.forEach(n => n.remove());

    const notification = document.createElement('div');
    notification.className = `drink-notification notification-${type}`;
    notification.innerHTML = message;

    // Style the notification
    notification.style.cssText = `
        position: fixed;
        top: 20px;
        right: 20px;
        background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
        color: white;
        padding: 15px 20px;
        border-radius: 8px;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        z-index: 10000;
        font-weight: 600;
        max-width: 300px;
        animation: slideIn 0.3s ease;
    `;

    document.body.appendChild(notification);

    // Auto remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.style.animation = 'slideOut 0.3s ease';
            setTimeout(() => notification.remove(), 300);
        }
    }, 5000);
}

// Request status checking
let statusCheckInterval = null;
let notifiedRequests = new Set(); // Track which requests we've already notified about

function startRequestStatusCheck() {
    if (statusCheckInterval) return; // Already running

    statusCheckInterval = setInterval(checkRequestStatus, 5000); // Check every 5 seconds
}

function checkRequestStatus() {
    fetch('{% url "bar:check_my_requests" %}')
    .then(response => response.json())
    .then(data => {
        if (data.success && data.requests.length > 0) {
            data.requests.forEach(request => {
                const requestKey = `${request.id}_${request.status}`;

                // Only show notification if we haven't already notified about this status
                if (!notifiedRequests.has(requestKey)) {
                    if (request.status === 'in_progress') {
                        showNotification(`🔄 Din ${request.drink_name} er nu i gang!`, 'info');
                        notifiedRequests.add(requestKey);
                    } else if (request.status === 'completed') {
                        showNotification(`✅ Din ${request.drink_name} er færdig!`, 'success');
                        notifiedRequests.add(requestKey);
                    } else if (request.status === 'cancelled') {
                        showNotification(`❌ Din ${request.drink_name} er blevet annulleret`, 'error');
                        notifiedRequests.add(requestKey);
                    }
                }
            });

            // Stop checking if no pending/in_progress requests
            const activeRequests = data.requests.filter(r => r.status === 'pending' || r.status === 'in_progress');
            if (activeRequests.length === 0) {
                clearInterval(statusCheckInterval);
                statusCheckInterval = null;

                // Clean up old notifications after 5 minutes
                setTimeout(() => {
                    notifiedRequests.clear();
                }, 300000);
            }
        } else if (data.requests.length === 0) {
            // No requests, stop checking
            clearInterval(statusCheckInterval);
            statusCheckInterval = null;
        }
    })
    .catch(error => {
        console.log('Status check failed:', error);
    });
}

// Start checking on page load if user can request drinks
{% if can_request_drinks %}
document.addEventListener('DOMContentLoaded', function() {
    // Check immediately and start interval if there are active requests
    checkRequestStatus();
});
{% endif %}

// PWA Splash Screen Management - DISABLED TO FIX BLURRING ISSUE
// document.addEventListener('DOMContentLoaded', function() {
//     // Hide splash screen after content is loaded
//     setTimeout(() => {
//         const splash = document.getElementById('pwa-splash');
//         if (splash) {
//             splash.classList.add('hidden');
//             setTimeout(() => {
//                 splash.style.display = 'none';
//             }, 500);
//         }
//     }, 2000); // Show for 2 seconds minimum
// });

// PWA specific enhancements - DISABLED TO FIX ISSUES
// if (window.matchMedia('(display-mode: standalone)').matches) {
//     // Running as PWA
//     console.log('🚀 Running as PWA');
//     document.body.classList.add('pwa-mode');
//
//     // Add PWA specific styles
//     const style = document.createElement('style');
//     style.textContent = `
//         .pwa-mode .navbar {
//             padding-top: env(safe-area-inset-top);
//         }
//         .pwa-mode .mobile-bottom-nav {
//             padding-bottom: env(safe-area-inset-bottom);
//         }
//     `;
//     document.head.appendChild(style);
// }

// Close modal when clicking outside of the modal content
window.onclick = function(event) {
    const modal = document.getElementById('drinkModal');
    if (event.target == modal) {
        closeModal();
    }
}

// FILTERING FUNCTIONS - REMOVED FOR REBUILD

// OLD FILTERING AND GRID FUNCTIONS - REMOVED FOR REBUILD

    // OLD INITIALIZATION CODE - REMOVED FOR REBUILD

    // FRESH CONTROLS & INTERFACE - COMPLETELY REBUILT
    document.addEventListener('DOMContentLoaded', function() {
        console.log('🚀 Initializing fresh controls system...');

        // Get all control elements
        const searchField = document.getElementById('searchField');
        const difficultyFilter = document.getElementById('difficultyFilter');
        const typeFilter = document.getElementById('typeFilter');
        const gridLayout = document.getElementById('gridLayout');
        const mainContainer = document.getElementById('mainContainer');

        // Verify elements exist
        if (!searchField || !difficultyFilter || !typeFilter || !gridLayout || !mainContainer) {
            console.error('❌ Some control elements not found!');
            return;
        }

        console.log('✅ All control elements found');

        // Grid Layout Function
        function updateGridLayout(size) {
            console.log(`🔧 Updating grid to ${size} columns`);

            // Remove all grid classes
            mainContainer.classList.remove('grid-4', 'grid-6', 'grid-8', 'grid-10', 'grid-12');

            // Add new grid class
            mainContainer.classList.add(`grid-${size}`);

            console.log(`✅ Grid updated to ${size} columns`);
        }

        // Search Function
        function applySearch() {
            const searchTerm = searchField.value.toLowerCase().trim();
            const drinkItems = document.querySelectorAll('.drink-item');

            console.log(`🔍 Searching for: "${searchTerm}"`);

            let visibleCount = 0;
            drinkItems.forEach(item => {
                const drinkName = item.querySelector('h3')?.textContent.toLowerCase() || '';

                if (searchTerm === '' || drinkName.includes(searchTerm)) {
                    item.style.display = '';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });

            console.log(`✅ Search complete: ${visibleCount} drinks visible`);
        }

        // Difficulty Filter Function
        function applyDifficultyFilter() {
            const difficulty = difficultyFilter.value;
            const drinkItems = document.querySelectorAll('.drink-item');

            console.log(`⚡ Filtering by difficulty: ${difficulty}`);

            let visibleCount = 0;
            drinkItems.forEach(item => {
                const ingredientsList = item.querySelector('.drink-card-info .ingredients-list');
                const ingredientCount = ingredientsList ? ingredientsList.querySelectorAll('li').length : 0;

                let showItem = true;
                if (difficulty === 'easy' && ingredientCount > 3) showItem = false;
                if (difficulty === 'medium' && (ingredientCount < 4 || ingredientCount > 5)) showItem = false;
                if (difficulty === 'hard' && ingredientCount < 6) showItem = false;

                if (showItem) {
                    item.style.display = '';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });

            console.log(`✅ Difficulty filter complete: ${visibleCount} drinks visible`);
        }

        // Type Filter Function
        function applyTypeFilter() {
            const type = typeFilter.value;
            const drinkItems = document.querySelectorAll('.drink-item');

            console.log(`🍹 Filtering by type: ${type}`);

            let visibleCount = 0;
            drinkItems.forEach(item => {
                const drinkType = item.getAttribute('data-drink-type') || 'drink';

                if (type === 'all' || drinkType === type) {
                    item.style.display = '';
                    visibleCount++;
                } else {
                    item.style.display = 'none';
                }
            });

            console.log(`✅ Type filter complete: ${visibleCount} drinks visible`);
        }

        // PERFORMANCE OPTIMIZED FILTERING
        let drinkCache = null;
        let filterTimeout = null;

        function buildDrinkCache() {
            if (drinkCache) return drinkCache;

            const drinkItems = document.querySelectorAll('.drink-item');
            drinkCache = Array.from(drinkItems).map(item => {
                const nameElement = item.querySelector('h3');
                const ingredientsList = item.querySelector('.drink-card-info .ingredients-list');

                return {
                    element: item,
                    name: nameElement ? nameElement.textContent.toLowerCase() : '',
                    type: item.getAttribute('data-drink-type') || 'drink',
                    ingredientCount: ingredientsList ? ingredientsList.querySelectorAll('li').length : 0
                };
            });

            return drinkCache;
        }

        // Debounced filter function for better performance
        function applyAllFilters() {
            clearTimeout(filterTimeout);
            filterTimeout = setTimeout(() => {
                const searchTerm = searchField.value.toLowerCase().trim();
                const difficulty = difficultyFilter.value;
                const type = typeFilter.value;
                const drinks = buildDrinkCache();

                let visibleCount = 0;

                // Use requestAnimationFrame for smooth UI updates
                requestAnimationFrame(() => {
                    drinks.forEach(drink => {
                        let showItem = true;

                        // Search filter
                        if (searchTerm !== '' && !drink.name.includes(searchTerm)) {
                            showItem = false;
                        }

                        // Type filter
                        if (type !== 'all' && drink.type !== type) {
                            showItem = false;
                        }

                        // Difficulty filter
                        if (difficulty === 'easy' && drink.ingredientCount > 3) showItem = false;
                        if (difficulty === 'medium' && (drink.ingredientCount < 4 || drink.ingredientCount > 5)) showItem = false;
                        if (difficulty === 'hard' && drink.ingredientCount < 6) showItem = false;

                        drink.element.style.display = showItem ? '' : 'none';
                        if (showItem) visibleCount++;
                    });
                });
            }, 150); // 150ms debounce for search
        }

        // OPTIMIZED EVENT LISTENERS
        gridLayout.addEventListener('change', function() {
            updateGridLayout(this.value);
        });

        searchField.addEventListener('input', applyAllFilters);
        difficultyFilter.addEventListener('change', applyAllFilters);

        // Debounced search input for better performance
        typeFilter.addEventListener('change', applyAllFilters);

        // Passive event listeners for better scroll performance
        document.addEventListener('scroll', function() {
            // Lazy load images on scroll if needed
        }, { passive: true });

        // Initialize default grid
        updateGridLayout('8');

        console.log('🎉 Fresh controls system initialized successfully!');
    });

    // Test function for grid layout
    function testGrid(size) {
        const mainContainer = document.getElementById('mainContainer');
        if (mainContainer) {
            mainContainer.classList.remove('grid-4', 'grid-6', 'grid-8', 'grid-10', 'grid-12');
            mainContainer.classList.add(`grid-${size}`);
            console.log(`🧪 Test: Grid set to ${size} columns`);

            // Update dropdown to match
            const gridLayout = document.getElementById('gridLayout');
            if (gridLayout) {
                gridLayout.value = size;
            }
        } else {
            console.error('❌ Test failed: mainContainer not found');
        }
    }

    // Make test function globally available
    window.testGrid = testGrid;

// FLOATING ACTION BUTTON FUNCTIONS
function toggleFabMenu() {
    const fabMenu = document.getElementById('fabMenu');
    const fabMain = document.getElementById('fabMain');

    fabMenu.classList.toggle('active');
    fabMain.style.transform = fabMenu.classList.contains('active') ? 'rotate(45deg)' : 'rotate(0deg)';
}

function scrollToTop() {
    window.scrollTo({ top: 0, behavior: 'smooth' });
    toggleFabMenu();
}

function toggleRandomDrink() {
    const drinkItems = document.querySelectorAll('.drink-item:not([style*="display: none"])');
    if (drinkItems.length > 0) {
        const randomIndex = Math.floor(Math.random() * drinkItems.length);
        const randomDrink = drinkItems[randomIndex];

        // Scroll to random drink
        randomDrink.scrollIntoView({ behavior: 'smooth', block: 'center' });

        // Highlight it briefly
        randomDrink.style.transform = 'scale(1.1)';
        randomDrink.style.transition = 'transform 0.3s ease';

        setTimeout(() => {
            randomDrink.style.transform = 'scale(1)';
        }, 1000);
    }
    toggleFabMenu();
}

let favoritesOnly = false;
function toggleFavorites() {
    favoritesOnly = !favoritesOnly;
    const drinkItems = document.querySelectorAll('.drink-item');

    drinkItems.forEach(item => {
        const isFavorite = item.querySelector('.favorite-icon')?.classList.contains('favorited');

        if (favoritesOnly) {
            if (!isFavorite) {
                item.style.display = 'none';
            }
        } else {
            item.style.display = '';
        }
    });

    // Update button appearance
    const favButton = document.querySelector('.fab-item[onclick="toggleFavorites()"]');
    if (favButton) {
        favButton.style.background = favoritesOnly ? '#ff6b6b' : 'rgba(255, 255, 255, 0.9)';
        favButton.innerHTML = favoritesOnly ? '💔' : '❤️';
    }

    toggleFabMenu();
}

// DEBUG FUNCTION - REMOVED FOR REBUILD

// FAVORITE FUNCTIONALITY
function toggleDrinkFavorite(drinkId, iconElement) {
    console.log(`🤍 Toggling favorite for drink ${drinkId}`);

    // Send to server
    fetch('/toggle-favorite/', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'X-CSRFToken': getCookie('csrftoken')
        },
        body: JSON.stringify({
            drink_id: drinkId
        })
    })
    .then(response => response.json())
    .then(data => {
        if (data.status === 'success') {
            // Update icon
            if (data.is_favorite) {
                iconElement.innerHTML = '❤️';
                iconElement.classList.add('favorited');
                console.log(`✅ Added to favorites: ${drinkId}`);
            } else {
                iconElement.innerHTML = '🤍';
                iconElement.classList.remove('favorited');
                console.log(`❌ Removed from favorites: ${drinkId}`);
            }
        } else {
            console.error('❌ Failed to toggle favorite:', data.message);
        }
    })
    .catch(error => {
        console.error('❌ Error toggling favorite:', error);
        // Fallback: just toggle the icon
        if (iconElement.classList.contains('favorited')) {
            iconElement.innerHTML = '🤍';
            iconElement.classList.remove('favorited');
        } else {
            iconElement.innerHTML = '❤️';
            iconElement.classList.add('favorited');
        }
    });
}

// DEBUG FUNKTIONER FOR FLIP KORT - ONLY FOR SUPERUSERS
let debugEnabled = {% if user.is_superuser %}true{% else %}false{% endif %};

function debugLog(message, data = null) {
    if (debugEnabled) {
        console.log('🐛 FLIP CARD DEBUG:', message, data);
        updateDebugPanel(message, data);
    }
}

function updateDebugPanel(message, data) {
    const debugContent = document.getElementById('debugContent');
    if (!debugContent) return;

    const timestamp = new Date().toLocaleTimeString();
    const dataStr = data ? JSON.stringify(data, null, 2) : '';

    debugContent.innerHTML += `
        <div style="border-bottom: 1px solid #333; padding: 5px 0; margin: 5px 0;">
            <strong style="color: #4caf50;">[${timestamp}]</strong><br>
            ${message}<br>
            ${dataStr ? `<pre style="color: #ffeb3b; font-size: 10px; margin: 5px 0;">${dataStr}</pre>` : ''}
        </div>
    `;
    debugContent.scrollTop = debugContent.scrollHeight;
}

function toggleDebugPanel() {
    const panel = document.getElementById('debugPanel');
    if (panel.style.display === 'none') {
        panel.style.display = 'block';
    } else {
        panel.style.display = 'none';
    }
}

// Tilføj debug event listeners til alle drink kort
document.addEventListener('DOMContentLoaded', function() {
    debugLog('DOM loaded, initializing flip card debug...');

    const drinkCards = document.querySelectorAll('.simple-card');
    debugLog(`Found ${drinkCards.length} drink cards`);

    drinkCards.forEach((card, index) => {
        // Debug hover events
        card.addEventListener('mouseenter', function() {
            debugLog(`Mouse entered card ${index + 1}`);

            const cardContent = card.querySelector('.card-content');
            const cardFront = card.querySelector('.card-front');
            const cardBack = card.querySelector('.card-back');
            const drinkName = cardFront ? cardFront.querySelector('h3') : null;
            const ingredientsList = cardBack ? cardBack.querySelector('.ingredients-list-back') : null;

            const debugData = {
                cardIndex: index + 1,
                drinkName: drinkName ? drinkName.innerText : 'N/A',
                hasCardContent: !!cardContent,
                hasCardFront: !!cardFront,
                hasCardBack: !!cardBack,
                hasIngredientsList: !!ingredientsList,
                ingredientsCount: ingredientsList ? ingredientsList.children.length : 0,
                cardBackVisible: cardBack ? window.getComputedStyle(cardBack).visibility : 'N/A',
                cardBackOpacity: cardBack ? window.getComputedStyle(cardBack).opacity : 'N/A',
                cardBackZIndex: cardBack ? window.getComputedStyle(cardBack).zIndex : 'N/A',
                cardBackTransform: cardBack ? window.getComputedStyle(cardBack).transform : 'N/A'
            };

            debugLog('Card hover details:', debugData);

            if (ingredientsList) {
                const ingredients = Array.from(ingredientsList.children).map(li => li.innerText);
                debugLog('Ingredients found:', ingredients);
            } else {
                debugLog('❌ No ingredients list found in card back!');
            }
        });

        card.addEventListener('mouseleave', function() {
            debugLog(`Mouse left card ${index + 1}`);
        });
    });
});

</script>

<!-- DEBUG PANEL - ONLY FOR SUPERUSERS -->
{% if user.is_superuser %}
<div id="debugPanel" style="position: fixed; top: 10px; right: 10px; background: rgba(0,0,0,0.9); color: white; padding: 15px; border-radius: 10px; z-index: 9999; max-width: 400px; font-family: monospace; font-size: 12px; max-height: 500px; overflow-y: auto; display: none;">
    <h4 style="margin: 0 0 10px 0; color: #ffd700;">🐛 Flip Card Debug</h4>
    <div id="debugContent" style="max-height: 300px; overflow-y: auto;">
        <p>Hover over et drink kort for at se debug info...</p>
    </div>
    <button onclick="toggleDebugPanel()" style="margin-top: 10px; padding: 5px 10px; background: #667eea; color: white; border: none; border-radius: 5px; cursor: pointer;">Skjul Debug</button>
    <button onclick="document.getElementById('debugContent').innerHTML = ''" style="margin-top: 10px; margin-left: 5px; padding: 5px 10px; background: #f44336; color: white; border: none; border-radius: 5px; cursor: pointer;">Ryd Log</button>
</div>

<!-- DEBUG TOGGLE BUTTON - ONLY FOR SUPERUSERS -->
<button onclick="toggleDebugPanel()" style="position: fixed; bottom: 20px; left: 20px; background: rgba(0,0,0,0.7); color: white; border: none; padding: 8px 12px; border-radius: 20px; font-size: 12px; cursor: pointer; z-index: 9998;">
    🐛 Debug
</button>
{% endif %}

<!-- PERFORMANCE OPTIMIZATIONS FOR TABLETS -->
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Optimize for tablet performance
    const isTablet = window.innerWidth >= 768 && window.innerWidth <= 1024;

    if (isTablet) {
        console.log('🚀 Tablet performance optimizations enabled');

        // Reduce animation complexity on tablets
        const style = document.createElement('style');
        style.textContent = `
            .simple-card .card-content { transition: transform 0.3s ease !important; }
            .drink-card-img { transition: transform 0.2s ease, filter 0.3s ease !important; }
            .card-front, .card-back { transition: none !important; }
        `;
        document.head.appendChild(style);

        // Optimize image loading with intersection observer
        const images = document.querySelectorAll('.drink-card-img[loading="lazy"]');
        if (images.length > 0) {
            const imageObserver = new IntersectionObserver((entries) => {
                entries.forEach(entry => {
                    if (entry.isIntersecting) {
                        entry.target.classList.add('loaded');
                        imageObserver.unobserve(entry.target);
                    }
                });
            }, { rootMargin: '100px' });

            images.forEach(img => imageObserver.observe(img));
        }
    }

    // Memory cleanup
    window.addEventListener('beforeunload', function() {
        if (window.drinkCache) window.drinkCache = null;
        if (window.filterTimeout) clearTimeout(window.filterTimeout);
    });
});
</script>

{% endblock %}
