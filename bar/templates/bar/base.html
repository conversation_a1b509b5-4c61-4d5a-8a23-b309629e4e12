{% load static %}
<!DOCTYPE html>
<html lang="da">
<head>
    <meta charset="UTF-8">
    <title>{% block title %}Min Bar{% endblock %}</title>
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">

    <!-- PWA Meta Tags - DISABLED TO FIX ISSUES -->
    <!-- <meta name="mobile-web-app-capable" content="yes"> -->
    <!-- <meta name="apple-mobile-web-app-capable" content="yes"> -->
    <!-- <meta name="apple-mobile-web-app-status-bar-style" content="black-translucent"> -->
    <meta name="theme-color" content="#667eea">
    <meta name="description" content="Administrer din hjemmebar og find drinks du kan lave">

    <!-- PWA Manifest - DISABLED -->
    <!-- <link rel="manifest" href="{% static 'bar/manifest.json' %}"> -->

    <!-- App Icons - Using emoji as temporary icon -->
    <link rel="icon" href="data:image/svg+xml,<svg xmlns=%22http://www.w3.org/2000/svg%22 viewBox=%220 0 100 100%22><text y=%22.9em%22 font-size=%2290%22>🍹</text></svg>">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Essential Performance Libraries Only -->
    <script src="https://unpkg.com/vanilla-lazyload@17.8.5/dist/lazyload.min.js" defer></script>

    <!-- Conditional Loading - Only Load What's Needed -->
    <script>
        // Lightweight performance optimization
        document.documentElement.style.transform = 'translateZ(0)';

        // Only load heavy libraries when needed
        window.loadLibraryOnDemand = function(url, callback) {
            const script = document.createElement('script');
            script.src = url;
            script.onload = callback;
            document.head.appendChild(script);
        };
    </script>

    <!-- Custom Styling for Modern Libraries -->
    <style>
        /* GLOBAL MOBILE FIXES - Prevent horizontal scroll and remove gaps */
        @media (max-width: 576px) {
            html, body {
                margin: 0 !important;
                padding: 0 !important;
                overflow-x: hidden !important;
                width: 100% !important;
                max-width: 100% !important;
            }

            .container, .container-fluid {
                padding: 0 !important;
                margin: 0 !important;
                width: 100% !important;
                max-width: 100% !important;
            }

            .row {
                margin: 0 !important;
                padding: 0 !important;
            }

            /* Remove all Bootstrap column gaps on mobile */
            .col, .col-1, .col-2, .col-3, .col-4, .col-5, .col-6,
            .col-7, .col-8, .col-9, .col-10, .col-11, .col-12,
            .col-auto, .col-sm, .col-md, .col-lg, .col-xl, .col-xxl {
                padding: 0 !important;
                margin: 0 !important;
            }
        }

        /* Essential Performance CSS Only */
        * {
            box-sizing: border-box;
        }

        img {
            max-width: 100%;
            height: auto;
        }
    </style>
    <style>
        body { padding-top: 4rem; }
        .navbar-brand { font-weight: bold; }
        body.dark-mode {
            background-color: #121212;
            color: #eee;
        }
        body.dark-mode .navbar,
        body.dark-mode .card,
        body.dark-mode .modal-box {
            background-color: #1f1f1f;
            color: #eee;
        }

        /* FIXED ADMIN DROPDOWN POSITIONING */
        .navbar .dropdown-menu {
            z-index: 9999 !important;
            position: absolute !important;
            top: 100% !important;
            right: 0 !important;
            left: auto !important;
        }

        .navbar .dropdown {
            position: relative !important;
        }
    </style>

    <!-- PWA & Mobile Styles -->
    <style>
        /* Mobile Bottom Navigation */
        .mobile-bottom-nav {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(20px);
            border-top: 1px solid rgba(0, 0, 0, 0.1);
            display: flex;
            justify-content: space-around;
            padding: 8px 0;
            z-index: 1000;
            box-shadow: 0 -2px 20px rgba(0, 0, 0, 0.1);
        }

        .mobile-bottom-nav .nav-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            text-decoration: none;
            color: #666;
            padding: 8px 12px;
            border-radius: 12px;
            transition: all 0.3s ease;
            min-width: 60px;
        }

        .mobile-bottom-nav .nav-item:hover,
        .mobile-bottom-nav .nav-item.active {
            color: #667eea;
            background: rgba(102, 126, 234, 0.1);
            transform: translateY(-2px);
        }

        .mobile-bottom-nav .nav-icon {
            font-size: 1.5rem;
            margin-bottom: 2px;
        }

        .mobile-bottom-nav .nav-label {
            font-size: 0.7rem;
            font-weight: 500;
        }

        /* PWA Install Prompt - COMPLETELY REMOVED */
        /* All PWA install CSS removed to fix issues */

        /* Mobile Optimizations */
        @media (max-width: 768px) {
            body {
                padding-bottom: 80px !important; /* Space for bottom nav */
                padding-top: 1rem !important; /* Less top padding */
            }

            .container {
                padding-left: 12px;
                padding-right: 12px;
            }

            /* Hide desktop navbar on mobile */
            .navbar {
                display: none !important;
            }

            /* Larger touch targets */
            .btn {
                min-height: 44px;
                padding: 12px 20px;
                font-size: 16px;
            }

            /* Better form inputs */
            .form-control,
            .form-select {
                min-height: 44px;
                font-size: 16px; /* Prevents zoom on iOS */
            }

            /* Card optimizations */
            .card {
                border-radius: 16px;
                margin-bottom: 16px;
            }

            /* Fix z-index issues on mobile */
            .control-dashboard,
            .dashboard-stats {
                position: relative !important;
                z-index: 1 !important;
            }

            /* Ensure content scrolls properly */
            .ingredient-grid,
            .bar-grid {
                position: relative;
                z-index: 1;
            }
        }

        /* Dark mode for mobile nav */
        body.dark-mode .mobile-bottom-nav {
            background: rgba(33, 37, 41, 0.95);
            border-top-color: rgba(255, 255, 255, 0.1);
        }

        body.dark-mode .mobile-bottom-nav .nav-item {
            color: #adb5bd;
        }

        body.dark-mode .mobile-bottom-nav .nav-item:hover,
        body.dark-mode .mobile-bottom-nav .nav-item.active {
            color: #667eea;
            background: rgba(102, 126, 234, 0.2);
        }

        /* PWA dark mode CSS removed */

        /* Toast Notifications */
        .toast-notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: #333;
            color: white;
            padding: 12px 20px;
            border-radius: 8px;
            z-index: 1002;
            transform: translateX(100%);
            opacity: 0;
            transition: all 0.3s ease;
        }

        .toast-notification.show {
            transform: translateX(0);
            opacity: 1;
        }

        .toast-success { background: #28a745; }
        .toast-info { background: #17a2b8; }
        .toast-warning { background: #ffc107; color: #333; }
        .toast-error { background: #dc3545; }

        /* PWA Install Banner - COMPLETELY REMOVED */
        /* All PWA install banner CSS removed to fix issues */

        /* Mobile Menu Modal */
        .mobile-menu-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.5);
            z-index: 9999;
            backdrop-filter: blur(5px);
        }

        .mobile-menu-content {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            border-radius: 20px 20px 0 0;
            padding: 20px;
            max-height: 70vh;
            overflow-y: auto;
            animation: slideUpIn 0.3s ease;
        }

        .mobile-menu-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 1px solid #eee;
        }

        .close-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #666;
            cursor: pointer;
        }

        .mobile-menu-item {
            display: flex;
            align-items: center;
            padding: 15px;
            text-decoration: none;
            color: #333;
            border-radius: 10px;
            margin-bottom: 10px;
            transition: background 0.2s ease;
            border: none;
            width: 100%;
            background: none;
            text-align: left;
        }

        .mobile-menu-item:hover {
            background: #f8f9fa;
            color: #333;
        }

        .mobile-menu-item .menu-icon {
            font-size: 1.5rem;
            margin-right: 15px;
            width: 30px;
            text-align: center;
        }

        .mobile-menu-item .menu-text {
            font-weight: 500;
        }

        .logout-btn {
            cursor: pointer;
        }

        @keyframes slideUpIn {
            from {
                transform: translateY(100%);
            }
            to {
                transform: translateY(0);
            }
        }
    </style>
</head>
<body>
<nav class="navbar navbar-expand-lg navbar-dark bg-dark fixed-top">
    <div class="container">
        <a class="navbar-brand" href=" {% url 'bar:drink_list' %} ">Min Bar</a>

        <!-- Burger menu knap til mobil -->
        <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarMenu">
            <span class="navbar-toggler-icon"></span>
        </button>

        <div class="collapse navbar-collapse" id="navbarMenu">
            <ul class="navbar-nav me-auto">
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'bar:drink_list' %}">Mine Drinks</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'bar:my_bar' %}">Rediger Bar</a>
                </li>
                <li class="nav-item">
                    <a class="nav-link" href="{% url 'bar:drink_planner' %}">🧮 Ingrediens Beregner</a>
                </li>
            </ul>
            <ul class="navbar-nav">
                {% if user.is_authenticated %}
                    <li class="nav-item">
                        <form method="post" action="{% url 'logout' %}" style="display:inline;">
                            {% csrf_token %}
                            <button type="submit" class="nav-link btn btn-link" style="padding: 0; margin: 0; border: none; color: inherit;">
                                Log ud ({{ user.username }})
                            </button>
                        </form>
                    </li>
                {% else %}
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'login' %}">Log ind</a>
                    </li>
                {% endif %}
                {% if user.is_authenticated and user.is_staff %}
                    <!-- FIXED ADMIN DROPDOWN - FULL BUTTON CLICKABLE -->
                    <li class="nav-item dropdown">
                        <button class="btn btn-outline-light ms-2 dropdown-toggle" type="button" data-bs-toggle="dropdown" aria-expanded="false" style="border: none; background: rgba(255,255,255,0.1); backdrop-filter: blur(10px);">
                            🔧 Admin
                        </button>
                        <ul class="dropdown-menu dropdown-menu-end" style="background: rgba(255,255,255,0.95); backdrop-filter: blur(15px); border: 1px solid rgba(255,255,255,0.2); border-radius: 12px; box-shadow: 0 8px 32px rgba(0,0,0,0.1); z-index: 9999 !important; position: absolute !important;">
                            <li><a class="dropdown-item" href="/admin/" style="border-radius: 8px; margin: 2px;">🔧 Django Admin</a></li>
                            <li><hr class="dropdown-divider" style="margin: 8px 0; border-color: rgba(0,0,0,0.1);"></li>
                            <li><a class="dropdown-item" href="{% url 'bar:bartender_dashboard' %}" style="border-radius: 8px; margin: 2px;">🍸 Bartender Dashboard</a></li>
                            <li><a class="dropdown-item" href="{% url 'bar:import_drinks' %}" style="border-radius: 8px; margin: 2px;">🤖 Auto Import</a></li>
                        </ul>
                    </li>
                {% endif %}
                <li class="nav-item">
                    <button class="btn btn-sm btn-outline-light ms-2" onclick="toggleDarkMode()">🌙</button>
                </li>
            </ul>
        </div>
    </div>
</nav>

<!-- Mobile Bottom Navigation -->
<div class="mobile-bottom-nav d-md-none">
    <a href="{% url 'bar:drink_list' %}" class="nav-item {% if request.resolver_match.url_name == 'drink_list' %}active{% endif %}">
        <div class="nav-icon">🍹</div>
        <div class="nav-label">Drinks</div>
    </a>
    <a href="{% url 'bar:my_bar' %}" class="nav-item {% if request.resolver_match.url_name == 'my_bar' %}active{% endif %}">
        <div class="nav-icon">🧪</div>
        <div class="nav-label">Min Bar</div>
    </a>
    {% if user.is_staff %}
    <a href="{% url 'bar:bartender_dashboard' %}" class="nav-item {% if request.resolver_match.url_name == 'bartender_dashboard' %}active{% endif %}">
        <div class="nav-icon">🍸</div>
        <div class="nav-label">Bartender</div>
    </a>
    {% endif %}
    <a href="#" class="nav-item" onclick="showMobileMenu()">
        <div class="nav-icon">⚙️</div>
        <div class="nav-label">Menu</div>
    </a>
</div>

<!-- Mobile Menu Modal -->
<div id="mobileMenuModal" class="mobile-menu-modal">
    <div class="mobile-menu-content">
        <div class="mobile-menu-header">
            <h5>Menu</h5>
            <button onclick="hideMobileMenu()" class="close-btn">&times;</button>
        </div>
        <div class="mobile-menu-items">
            <a href="{% url 'bar:drink_planner' %}" class="mobile-menu-item">
                <div class="menu-icon">🧮</div>
                <div class="menu-text">Ingrediens Beregner</div>
            </a>
            {% if user.is_staff %}
            <a href="/admin/" class="mobile-menu-item">
                <div class="menu-icon">🔧</div>
                <div class="menu-text">Django Admin</div>
            </a>
            <a href="{% url 'bar:import_drinks' %}" class="mobile-menu-item">
                <div class="menu-icon">🤖</div>
                <div class="menu-text">Auto Import</div>
            </a>
            {% endif %}
            <div class="mobile-menu-item" onclick="toggleDarkMode()">
                <div class="menu-icon">🌙</div>
                <div class="menu-text">Dark Mode</div>
            </div>
            {% if user.is_authenticated %}
            <form method="post" action="{% url 'logout' %}" style="margin: 0;">
                {% csrf_token %}
                <button type="submit" class="mobile-menu-item logout-btn">
                    <div class="menu-icon">🚪</div>
                    <div class="menu-text">Log ud ({{ user.username }})</div>
                </button>
            </form>
            {% endif %}
        </div>
    </div>
</div>

<!-- PWA Install Button - DISABLED -->
<!--
<div id="installPrompt" class="install-prompt d-none">
    <div class="install-content">
        <div class="install-icon">📱</div>
        <div class="install-text">
            <h6>Installer MyBar App</h6>
            <p>Få hurtig adgang fra din hjemmeskærm</p>
        </div>
        <button id="installBtn" class="btn btn-primary btn-sm">Installer</button>
        <button id="dismissBtn" class="btn btn-outline-secondary btn-sm">Senere</button>
    </div>
</div>
-->

<div class="container">
    {% block content %}{% endblock %}
</div>

<script>
    function toggleDarkMode() {
        document.body.classList.toggle('dark-mode');
        localStorage.setItem('darkMode', document.body.classList.contains('dark-mode'));
    }

    // Mobile Menu Functions
    function showMobileMenu() {
        document.getElementById('mobileMenuModal').style.display = 'block';
    }

    function hideMobileMenu() {
        document.getElementById('mobileMenuModal').style.display = 'none';
    }

    // Close mobile menu when clicking outside
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('mobileMenuModal');
        if (event.target === modal) {
            hideMobileMenu();
        }
    });

    window.onload = function() {
        if (localStorage.getItem('darkMode') === 'true') {
            document.body.classList.add('dark-mode');
        }

        // PWA features disabled
        // initPWA();
    };

    // PWA Installation and Service Worker - COMPLETELY DISABLED
    // let deferredPrompt;
    //
    // function initPWA() {
    //     // Register service worker - DISABLED FOR NOW
    //     /*
    //     if ('serviceWorker' in navigator) {
    //         navigator.serviceWorker.register('/static/sw.js')
    //             .then(registration => {
    //                 console.log('✅ Service Worker registered:', registration);
    //             })
    //             .catch(error => {
    //                 console.log('❌ Service Worker registration failed:', error);
    //             });
    //     }
    //     */
    //
    //     // Handle install prompt
    //     window.addEventListener('beforeinstallprompt', (e) => {
    //         e.preventDefault();
    //         deferredPrompt = e;
    //         showInstallPrompt();
    //     });
    //
    //     // Handle successful installation
    //     window.addEventListener('appinstalled', (evt) => {
    //         console.log('✅ MyBar app installed successfully');
    //         hideInstallPrompt();
    //         showToast('App installeret! 🎉', 'success');
    //     });
    // }

    // PWA Install functions - DISABLED
    // function showInstallPrompt() {
    //     const prompt = document.getElementById('installPrompt');
    //     if (prompt) {
    //         prompt.classList.remove('d-none');
    //         prompt.classList.add('show');
    //     }
    // }
    //
    // function hideInstallPrompt() {
    //     const prompt = document.getElementById('installPrompt');
    //     if (prompt) {
    //         prompt.classList.add('d-none');
    //         prompt.classList.remove('show');
    //     }
    // }

    // Install button click - DISABLED
    // document.addEventListener('DOMContentLoaded', function() {
    //     const installBtn = document.getElementById('installBtn');
    //     const dismissBtn = document.getElementById('dismissBtn');
    //
    //     if (installBtn) {
    //         installBtn.addEventListener('click', async () => {
    //             if (deferredPrompt) {
    //                 deferredPrompt.prompt();
    //                 const { outcome } = await deferredPrompt.userChoice;
    //
    //                 if (outcome === 'accepted') {
    //                     console.log('✅ User accepted install prompt');
    //                 } else {
    //                     console.log('❌ User dismissed install prompt');
    //                 }
    //
    //                 deferredPrompt = null;
    //                 hideInstallPrompt();
    //             }
    //         });
    //     }
    //
    //     if (dismissBtn) {
    //         dismissBtn.addEventListener('click', () => {
    //             hideInstallPrompt();
    //             localStorage.setItem('installPromptDismissed', 'true');
    //         });
    //     }
    // });

    function togglePartyMode() {
        // Party mode functionality - to be implemented
        showToast('Party Mode kommer snart! 🎉', 'info');
    }

    function showToast(message, type = 'info') {
        // Simple toast notification
        const toast = document.createElement('div');
        toast.className = `toast-notification toast-${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);

        setTimeout(() => {
            toast.classList.add('show');
        }, 100);

        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, 3000);
    }

    // PWA Service Worker Registration - DISABLED TO FIX ISSUES
    // if ('serviceWorker' in navigator) {
    //     window.addEventListener('load', () => {
    //         navigator.serviceWorker.register('{% static "bar/sw.js" %}')
    //             .then(registration => {
    //                 console.log('✅ Service Worker registered:', registration.scope);
    //
    //                 // Check for updates
    //                 registration.addEventListener('updatefound', () => {
    //                     const newWorker = registration.installing;
    //                     newWorker.addEventListener('statechange', () => {
    //                         if (newWorker.state === 'installed' && navigator.serviceWorker.controller) {
    //                             showToast('App opdatering tilgængelig! Genstart for at opdatere.', 'info');
    //                         }
    //                     });
    //                 });
    //             })
    //             .catch(error => {
    //                 console.log('❌ Service Worker registration failed:', error);
    //             });
    //     });

        // Handle app install prompt - DISABLED
        // let deferredPrompt;
        // window.addEventListener('beforeinstallprompt', (e) => {
        //     e.preventDefault();
        //     deferredPrompt = e;
        //
        //     // Show install button or banner
        //     showInstallPrompt();
        // });

        // Install functions - DISABLED
        // function showInstallPrompt() {
        //     const installBanner = document.createElement('div');
        //     installBanner.className = 'install-banner';
        //     installBanner.innerHTML = `
        //         <div class="install-content">
        //             <span>📱 Installer MyBar som app</span>
        //             <button onclick="installApp()" class="btn btn-sm btn-primary">Installer</button>
        //             <button onclick="dismissInstall()" class="btn btn-sm btn-outline-secondary">Senere</button>
        //         </div>
        //     `;
        //     document.body.appendChild(installBanner);
        // }
        //
        // window.installApp = async () => {
        //     if (deferredPrompt) {
        //         deferredPrompt.prompt();
        //         const { outcome } = await deferredPrompt.userChoice;
        //         console.log('Install prompt outcome:', outcome);
        //         deferredPrompt = null;
        //         document.querySelector('.install-banner')?.remove();
        //     }
        // };
        //
        // window.dismissInstall = () => {
        //     document.querySelector('.install-banner')?.remove();
        //     localStorage.setItem('installDismissed', Date.now());
        // };
    // }
</script>
</body>
</html>
