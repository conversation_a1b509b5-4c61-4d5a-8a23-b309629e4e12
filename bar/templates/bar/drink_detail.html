{% extends 'bar/base.html' %}
{% load static %}

{% block title %}{{ drink.name }}{% endblock %}

{% block content %}
<div class="container mt-5">
    <div class="row">
        <div class="col-md-8 mx-auto">
            <div class="card shadow-lg">
                {% if drink.image %}
                    <img src="{{ drink.image.url }}" class="card-img-top" alt="{{ drink.name }}" style="max-height: 400px; object-fit: cover;">
                {% endif %}
                <div class="card-body">
                    <h1 class="card-title">{{ drink.name }}</h1>
                    <p class="text-muted">{{ drink.get_drink_type_display }}</p>
                    <hr>
                    <div class="mt-4 recipe-content">{{ drink.get_full_recipe|safe }}</div>
                    <a href="{% url 'bar:drink_list' %}" class="btn btn-secondary mt-4">Tilbage til listen</a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}