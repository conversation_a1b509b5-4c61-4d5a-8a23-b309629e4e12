from django.contrib import admin
from django import forms
from django.utils.html import format_html
from django.utils.safestring import mark_safe
from django.http import HttpResponse
from .models import (Ingredient, Drink, UserBar, Comment, DrinkPlan, DrinkPlanItem,
                     DrinkIngredient, DrinkIngredientGroup, UserFavorite, DrinkRating,
                     IngredientGroup, DrinkFavorite, DrinkRequestProfile, DrinkRequest)

# Try to import custom widget, with a fallback if it's not found
try:
    from .widgets import DrinkTypeEmojiWidget
except ImportError:
    class DrinkTypeEmojiWidget(forms.Select): # Fallback to a standard select if widget not found
        pass

# Admin class for Ingredient with substitutes
@admin.register(Ingredient)
class IngredientAdmin(admin.ModelAdmin):
    list_display = ('name_with_icon', 'thumbnail', 'short_description', 'substitute_count')
    search_fields = ('name',)
    filter_horizontal = ('substitutes',)
    readonly_fields = ('thumbnail',)
    list_per_page = 20

    fieldsets = (
        ('Grundlæggende info', {
            'fields': ('name', 'image', 'thumbnail', 'description')
        }),
        ('Erstatninger', {
            'fields': ('substitutes',),
            'description': 'Vælg ingredienser der kan erstatte denne (f.eks. Vodka kan erstatte Gin i nogle drinks)'
        }),
    )

    def name_with_icon(self, obj):
        return format_html('🥃 <strong style="color: #2c3e50;">{}</strong>', obj.name)
    name_with_icon.short_description = "Ingrediens"
    name_with_icon.admin_order_field = 'name'

    def thumbnail(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="width: 60px; height: 60px; object-fit: cover; '
                'border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" />',
                obj.image.url
            )
        return format_html('<div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea, #764ba2); '
                          'border-radius: 12px; display: flex; align-items: center; justify-content: center; '
                          'color: white; font-size: 24px;">🥃</div>')
    thumbnail.short_description = "📸 Billede"

    def short_description(self, obj):
        if obj.description:
            preview = obj.description[:50] + "..." if len(obj.description) > 50 else obj.description
            return format_html('<span style="color: #666; font-style: italic;">{}</span>', preview)
        return format_html('<span style="color: #ccc;">Ingen beskrivelse</span>')
    short_description.short_description = "📝 Beskrivelse"

    def substitute_count(self, obj):
        count = obj.substitutes.count()
        if count > 0:
            return format_html('<span style="background: #e8f5e8; color: #2e7d32; padding: 4px 8px; '
                             'border-radius: 12px; font-size: 12px; font-weight: 600;">🔄 {} erstatninger</span>', count)
        return format_html('<span style="color: #999;">Ingen erstatninger</span>')
    substitute_count.short_description = "🔄 Erstatninger"

# Admin class for IngredientGroup
@admin.register(IngredientGroup)
class IngredientGroupAdmin(admin.ModelAdmin):
    list_display = ('name_with_icon', 'ingredient_count', 'ingredient_preview')
    filter_horizontal = ('ingredients',)
    search_fields = ('name',)
    list_per_page = 20

    def name_with_icon(self, obj):
        return format_html('🏷️ <strong style="color: #2c3e50;">{}</strong>', obj.name)
    name_with_icon.short_description = "Gruppe"
    name_with_icon.admin_order_field = 'name'

    def ingredient_count(self, obj):
        count = obj.ingredients.count()
        if count > 0:
            return format_html('<span style="background: #e3f2fd; color: #1976d2; padding: 4px 8px; '
                             'border-radius: 12px; font-size: 12px; font-weight: 600;">📦 {} ingredienser</span>', count)
        return format_html('<span style="color: #999;">Tom gruppe</span>')
    ingredient_count.short_description = "📦 Antal"

    def ingredient_preview(self, obj):
        ingredients = obj.ingredients.all()[:3]
        if ingredients:
            names = [ing.name for ing in ingredients]
            preview = ", ".join(names)
            if obj.ingredients.count() > 3:
                preview += " (+{} flere)".format(obj.ingredients.count() - 3)
            return format_html('<span style="color: #666; font-style: italic;">{}</span>', preview)
        return format_html('<span style="color: #ccc;">Ingen ingredienser</span>')
    ingredient_preview.short_description = "🥃 Ingredienser"

# Inline for DrinkIngredient
class DrinkIngredientInline(admin.TabularInline):
    model = DrinkIngredient
    extra = 1
    fields = ('ingredient', 'amount', 'unit', 'is_required')
    verbose_name = "Ingrediens med mængde"
    verbose_name_plural = "Ingredienser med mængder (vises automatisk i opskriften)"

# Inline for DrinkIngredientGroup
class DrinkIngredientGroupInline(admin.TabularInline):
    model = DrinkIngredientGroup
    extra = 1
    fields = ('ingredient_group', 'amount', 'unit', 'is_required')
    verbose_name = "Ingrediens gruppe med mængde"
    verbose_name_plural = "Ingrediens grupper med mængder (vælg 1 fra hver gruppe)"

# Import custom widgets
try:
    from .admin_widgets import ImagePositionWidget, ImageZoomWidget
except ImportError:
    # Fallback widgets if custom widgets not available
    ImagePositionWidget = forms.NumberInput
    ImageZoomWidget = forms.NumberInput

# Form for Drink model (to use custom widgets)
class DrinkForm(forms.ModelForm):
    class Meta:
        model = Drink
        fields = '__all__'
        widgets = {
            'drink_type': DrinkTypeEmojiWidget,
            'image_position_x': ImagePositionWidget(),
            'image_position_y': ImagePositionWidget(),
            'image_zoom': ImageZoomWidget(),
        }

# Admin class for Drink
@admin.register(Drink)
class DrinkAdmin(admin.ModelAdmin):
    form = DrinkForm
    list_display = ('name_with_icon', 'display_type', 'thumbnail', 'ingredient_summary', 'rating_display')
    search_fields = ('name',)
    list_filter = ('drink_type', 'ingredient_groups', 'required_ingredients')
    readonly_fields = ('thumbnail', 'full_recipe_preview')
    inlines = [DrinkIngredientInline, DrinkIngredientGroupInline]
    list_per_page = 20
    actions = ['make_featured', 'duplicate_drinks', 'export_recipes']

    def make_featured(self, request, queryset):
        """Mark selected drinks as featured"""
        count = queryset.update(is_featured=True)
        self.message_user(request, f'{count} drinks marked as featured.')
    make_featured.short_description = "⭐ Mark as featured"

    def duplicate_drinks(self, request, queryset):
        """Duplicate selected drinks"""
        count = 0
        for drink in queryset:
            # Create copy
            drink.pk = None
            drink.name = f"{drink.name} (Copy)"
            drink.save()
            count += 1
        self.message_user(request, f'{count} drinks duplicated.')
    duplicate_drinks.short_description = "📋 Duplicate drinks"

    def export_recipes(self, request, queryset):
        """Export recipes as text"""
        recipes = []
        for drink in queryset:
            recipes.append(f"=== {drink.name} ===\n{drink.get_full_recipe()}\n")

        response = HttpResponse('\n'.join(recipes), content_type='text/plain')
        response['Content-Disposition'] = 'attachment; filename="recipes.txt"'
        return response
    export_recipes.short_description = "📄 Export recipes"

    fieldsets = (
        ('Grundlæggende info', {
            'fields': ('name', 'drink_type', 'image', 'thumbnail')
        }),
        ('📸 Billede indstillinger', {
            'fields': ('image_position_x', 'image_position_y', 'image_zoom'),
            'description': 'Juster hvordan billedet vises i drink kortene. Ændringer vises øjeblikkeligt på hjemmesiden.',
        }),
        ('Opskrift', {
            'fields': ('recipe', 'full_recipe_preview')
        }),

    )

    def name_with_icon(self, obj):
        return format_html('🍹 <strong style="color: #2c3e50;">{}</strong>', obj.name)
    name_with_icon.short_description = "Drink"
    name_with_icon.admin_order_field = 'name'

    def display_type(self, obj):
        emoji_map = {
            'cocktail': ('🍸', 'Cocktail', '#e91e63'),
            'shot': ('🔫', 'Shot', '#ff5722'),
            'nonalcoholic': ('🚫', 'Alkoholfri', '#4caf50'),
            'drink': ('🍹', 'Drink', '#2196f3'),
        }
        emoji, name, color = emoji_map.get(obj.drink_type, ('🍹', 'Drink', '#2196f3'))
        return format_html('<span style="background: {}; color: white; padding: 4px 8px; '
                          'border-radius: 12px; font-size: 12px; font-weight: 600;">{} {}</span>',
                          color, emoji, name)
    display_type.short_description = "🏷️ Type"

    # Corrected thumbnail method: Removed 'allow_tags=True' from @admin.display
    @admin.display(description="📸 Billede")
    def thumbnail(self, obj):
        if obj.image:
            return format_html(
                '<img src="{}" style="width: 60px; height: 60px; object-fit: cover; '
                'border-radius: 12px; box-shadow: 0 4px 8px rgba(0,0,0,0.1);" />',
                obj.image.url
            )
        return format_html('<div style="width: 60px; height: 60px; background: linear-gradient(135deg, #667eea, #764ba2); '
                          'border-radius: 12px; display: flex; align-items: center; justify-content: center; '
                          'color: white; font-size: 24px;">🍹</div>')

    def short_recipe(self, obj):
        from django.utils.html import strip_tags
        return strip_tags(obj.recipe)[:50] + "..." if obj.recipe else "–"
    short_recipe.short_description = "Opskrift (kort)"

    def ingredient_summary(self, obj):
        ingredient_count = obj.drink_ingredients.count()
        group_count = obj.drink_ingredient_groups.count()

        parts = []
        if ingredient_count > 0:
            parts.append(f"{ingredient_count} ingredienser")
        if group_count > 0:
            parts.append(f"{group_count} grupper")

        if parts:
            summary = " + ".join(parts)
            return format_html('<span style="background: #f3e5f5; color: #7b1fa2; padding: 4px 8px; '
                             'border-radius: 12px; font-size: 12px; font-weight: 600;">🧪 {}</span>', summary)
        return format_html('<span style="color: #999;">Ingen ingredienser</span>')
    ingredient_summary.short_description = "🧪 Ingredienser"

    def rating_display(self, obj):
        try:
            avg_rating = float(obj.get_average_rating() or 0)
            if avg_rating > 0:
                stars = "⭐" * int(avg_rating)
                return format_html('<span style="background: #fff3e0; color: #f57c00; padding: 4px 8px; '
                                 'border-radius: 12px; font-size: 12px; font-weight: 600;">{} {:.1f}</span>',
                                 stars, avg_rating)
        except (ValueError, TypeError):
            pass
        return format_html('<span style="color: #999;">Ingen bedømmelser</span>')
    rating_display.short_description = "⭐ Bedømmelse"

    @admin.display(description="📖 Komplet opskrift preview")
    def full_recipe_preview(self, obj):
        full_recipe = obj.get_full_recipe()
        return format_html('<div style="max-height: 300px; overflow-y: auto; border: 2px solid #667eea; '
                          'border-radius: 12px; padding: 20px; background: linear-gradient(135deg, #f8f9ff, #fff); '
                          'box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1);">{}</div>', full_recipe)

    readonly_fields = ('thumbnail', 'full_recipe_preview')




# SIMPLIFIED DRINK REQUEST ADMIN
@admin.register(DrinkRequestProfile)
class DrinkRequestProfileAdmin(admin.ModelAdmin):
    list_display = ('name_with_icon', 'requester_info', 'bartender_info', 'status_badge', 'request_count', 'created_at')
    list_filter = ('is_active', 'created_at')
    search_fields = ('name', 'requester_user__username', 'bartender_user__username')
    readonly_fields = ('created_at',)

    fieldsets = (
        ('📋 Profil Information', {
            'fields': ('name', 'is_active')
        }),
        ('👥 Brugere', {
            'fields': ('requester_user', 'bartender_user'),
            'description': 'Vælg hvem der kan bestille drinks og hvem der kan se requests'
        }),
        ('📊 Metadata', {
            'fields': ('created_at',),
            'classes': ('collapse',)
        }),
    )

    def name_with_icon(self, obj):
        icon = '🍹' if obj.is_active else '🍹💤'
        return format_html('{} <strong>{}</strong>', icon, obj.name)
    name_with_icon.short_description = "Profil"

    def requester_info(self, obj):
        return format_html('👤 <strong>{}</strong>', obj.requester_user.username)
    requester_info.short_description = "Bestiller"

    def bartender_info(self, obj):
        return format_html('👨‍🍳 <strong>{}</strong>', obj.bartender_user.username)
    bartender_info.short_description = "Bartender"

    def status_badge(self, obj):
        if obj.is_active:
            return format_html('<span style="background: #e8f5e8; color: #2e7d32; padding: 4px 8px; '
                              'border-radius: 12px; font-size: 12px; font-weight: 600;">✅ Aktiv</span>')
        else:
            return format_html('<span style="background: #ffebee; color: #c62828; padding: 4px 8px; '
                              'border-radius: 12px; font-size: 12px; font-weight: 600;">❌ Inaktiv</span>')
    status_badge.short_description = "Status"

    def request_count(self, obj):
        count = obj.drink_requests.count()
        pending_count = obj.drink_requests.filter(status='pending').count()
        if pending_count > 0:
            return format_html('<span style="background: #fff3e0; color: #f57c00; padding: 4px 8px; '
                              'border-radius: 12px; font-size: 12px; font-weight: 600;">🍹 {} total (⏳ {} afventer)</span>',
                              count, pending_count)
        else:
            return format_html('<span style="background: #f3e5f5; color: #7b1fa2; padding: 4px 8px; '
                              'border-radius: 12px; font-size: 12px; font-weight: 600;">🍹 {} requests</span>', count)
    request_count.short_description = "Requests"


@admin.register(DrinkRequest)
class DrinkRequestAdmin(admin.ModelAdmin):
    list_display = ('drink_with_icon', 'requester_info', 'guest_info', 'profile_name', 'status_badge', 'requested_at')
    list_filter = ('status', 'profile', 'requested_at', 'requester')
    search_fields = ('drink__name', 'guest_name', 'table_number', 'special_notes', 'requester__username')
    readonly_fields = ('requested_at', 'started_at', 'completed_at')

    fieldsets = (
        ('🍹 Request Information', {
            'fields': ('profile', 'drink', 'requester')
        }),
        ('👤 Gæst Information', {
            'fields': ('guest_name', 'table_number', 'special_notes')
        }),
        ('📊 Status', {
            'fields': ('status',)
        }),
        ('⏰ Timestamps', {
            'fields': ('requested_at', 'started_at', 'completed_at'),
            'classes': ('collapse',)
        }),
    )

    def drink_with_icon(self, obj):
        return format_html('🍹 <strong>{}</strong>', obj.drink.name)
    drink_with_icon.short_description = "Drink"

    def requester_info(self, obj):
        return format_html('👤 <strong>{}</strong>', obj.requester.username)
    requester_info.short_description = "Bestiller"

    def guest_info(self, obj):
        guest = obj.guest_name or "Ingen gæst info"
        table = f" (Bord {obj.table_number})" if obj.table_number else ""
        return format_html('{}<small>{}</small>', guest, table)
    guest_info.short_description = "Gæst"

    def profile_name(self, obj):
        return format_html('📋 {}', obj.profile.name)
    profile_name.short_description = "Profil"

    def status_badge(self, obj):
        status_colors = {
            'pending': ('⏳', '#fff3e0', '#f57c00'),
            'in_progress': ('🔄', '#e8f5e8', '#2e7d32'),
            'completed': ('✅', '#e8f5e8', '#2e7d32'),
            'cancelled': ('❌', '#ffebee', '#c62828'),
        }
        emoji, bg_color, text_color = status_colors.get(obj.status, ('❓', '#f5f5f5', '#666'))
        return format_html('<span style="background: {}; color: {}; padding: 4px 8px; '
                          'border-radius: 12px; font-size: 12px; font-weight: 600;">{} {}</span>',
                          bg_color, text_color, emoji, obj.get_status_display())
    status_badge.short_description = "Status"

    actions = ['mark_in_progress', 'mark_completed', 'mark_cancelled']

    def mark_in_progress(self, request, queryset):
        from django.utils import timezone
        updated = queryset.filter(status='pending').update(
            status='in_progress',
            started_at=timezone.now()
        )
        self.message_user(request, f'{updated} requests markeret som "I gang"')
    mark_in_progress.short_description = "Markér som 'I gang'"

    def mark_completed(self, request, queryset):
        from django.utils import timezone
        updated = queryset.exclude(status='completed').update(
            status='completed',
            completed_at=timezone.now()
        )
        self.message_user(request, f'{updated} requests markeret som "Færdig"')
    mark_completed.short_description = "Markér som 'Færdig'"

    def mark_cancelled(self, request, queryset):
        updated = queryset.exclude(status='cancelled').update(status='cancelled')
        self.message_user(request, f'{updated} requests markeret som "Annulleret"')
    mark_cancelled.short_description = "Markér som 'Annulleret'"


# Custom Admin Site Configuration
admin.site.site_header = "🍹 MyBar Administration"
admin.site.site_title = "MyBar Admin"
admin.site.index_title = "Velkommen til MyBar Admin Panel"

# Enhanced admin with custom actions
class EnhancedModelAdmin(admin.ModelAdmin):
    """Base admin class with enhanced features"""

    def get_queryset(self, request):
        qs = super().get_queryset(request)
        # Add performance optimizations
        if hasattr(self.model, 'select_related_fields'):
            qs = qs.select_related(*self.model.select_related_fields)
        if hasattr(self.model, 'prefetch_related_fields'):
            qs = qs.prefetch_related(*self.model.prefetch_related_fields)
        return qs

    def save_model(self, request, obj, form, change):
        """Enhanced save with user tracking"""
        if not change:  # Creating new object
            if hasattr(obj, 'created_by'):
                obj.created_by = request.user
        if hasattr(obj, 'updated_by'):
            obj.updated_by = request.user
        super().save_model(request, obj, form, change)

    class Media:
        css = {
            'all': ('admin/css/enhanced_admin.css',)
        }
        js = ('admin/js/enhanced_admin.js',)

# Register the remaining models with enhanced display
@admin.register(UserBar)
class UserBarAdmin(admin.ModelAdmin):
    list_display = ('user_with_icon', 'ingredient_count')
    search_fields = ('user__username', 'user__email')
    filter_horizontal = ('ingredients',)

    def user_with_icon(self, obj):
        return format_html('👤 <strong>{}</strong>', obj.user.username)
    user_with_icon.short_description = "Bruger"

    def ingredient_count(self, obj):
        count = obj.ingredients.count()
        return format_html('<span style="background: #e8f5e8; color: #2e7d32; padding: 4px 8px; '
                          'border-radius: 12px; font-size: 12px; font-weight: 600;">🥃 {} ingredienser</span>', count)
    ingredient_count.short_description = "Bar størrelse"

@admin.register(UserFavorite)
class FavoriteAdmin(admin.ModelAdmin):
    list_display = ('user_with_icon', 'drink_with_icon', 'created_display')
    list_filter = ('drink__drink_type',)
    search_fields = ('user__username', 'drink__name')

    def user_with_icon(self, obj):
        return format_html('👤 {}', obj.user.username)
    user_with_icon.short_description = "Bruger"

    def drink_with_icon(self, obj):
        return format_html('🍹 {}', obj.drink.name)
    drink_with_icon.short_description = "Favorit drink"

    def created_display(self, obj):
        return format_html('❤️ Favorit')
    created_display.short_description = "Status"

@admin.register(DrinkRating)
class RatingAdmin(admin.ModelAdmin):
    list_display = ('user_with_icon', 'drink_with_icon', 'rating_display', 'created_at')
    list_filter = ('rating', 'created_at', 'drink__drink_type')
    search_fields = ('user__username', 'drink__name')

    def user_with_icon(self, obj):
        return format_html('👤 {}', obj.user.username)
    user_with_icon.short_description = "Bruger"

    def drink_with_icon(self, obj):
        return format_html('🍹 {}', obj.drink.name)
    drink_with_icon.short_description = "Drink"

    def rating_display(self, obj):
        stars = "⭐" * obj.rating
        return format_html('<span style="background: #fff3e0; color: #f57c00; padding: 4px 8px; '
                          'border-radius: 12px; font-size: 12px; font-weight: 600;">{}</span>', stars)
    rating_display.short_description = "Bedømmelse"

@admin.register(Comment)
class CommentAdmin(admin.ModelAdmin):
    list_display = ('user_with_icon', 'drink_with_icon', 'text_preview', 'created_at')
    list_filter = ('created_at', 'drink__drink_type')
    search_fields = ('user__username', 'drink__name', 'text')
    readonly_fields = ('created_at',)

    def user_with_icon(self, obj):
        return format_html('👤 {}', obj.user.username)
    user_with_icon.short_description = "Bruger"

    def drink_with_icon(self, obj):
        return format_html('🍹 {}', obj.drink.name)
    drink_with_icon.short_description = "Drink"

    def text_preview(self, obj):
        preview = obj.text[:50] + "..." if len(obj.text) > 50 else obj.text
        return format_html('<span style="background: #f3e5f5; color: #7b1fa2; padding: 4px 8px; '
                          'border-radius: 12px; font-size: 12px;">💬 {}</span>', preview)
    text_preview.short_description = "Anmeldelse"

# DRINK PLANNER ADMIN - Easy to remove if not wanted
class DrinkPlanItemInline(admin.TabularInline):
    model = DrinkPlanItem
    extra = 0

@admin.register(DrinkPlan)
class DrinkPlanAdmin(admin.ModelAdmin):
    list_display = ('name_with_icon', 'user_with_icon', 'guest_count_display', 'created_at')
    list_filter = ('created_at', 'guest_count')
    search_fields = ('name', 'user__username')
    inlines = [DrinkPlanItemInline]

    def name_with_icon(self, obj):
        return format_html('🎉 <strong>{}</strong>', obj.name)
    name_with_icon.short_description = "Event"
    name_with_icon.admin_order_field = 'name'

    def user_with_icon(self, obj):
        return format_html('👤 {}', obj.user.username)
    user_with_icon.short_description = "Arrangør"

    def guest_count_display(self, obj):
        return format_html('<span style="background: #e3f2fd; color: #1976d2; padding: 4px 8px; '
                          'border-radius: 12px; font-size: 12px; font-weight: 600;">👥 {} gæster</span>', obj.guest_count)
    guest_count_display.short_description = "Gæster"