import json
import logging
from datetime import datetime
from django.shortcuts import render, redirect, get_object_or_404
from django.contrib.auth.decorators import login_required
from django.http import JsonResponse
from django.views.decorators.http import require_POST
from django.views.decorators.csrf import csrf_exempt
from django.contrib.admin.views.decorators import staff_member_required
from django.core.management import call_command
from .models import (Ingredient, Drink, UserBar, UserFavorite, DrinkRating, Comment,
                     DrinkPlan, DrinkPlanItem, DrinkIngredient, DrinkIngredientGroup,
                     DrinkRequestProfile, DrinkRequest)
from django.db.models import Avg, Q
from django.db import transaction

# Setup logger
logger = logging.getLogger(__name__)
import json
import logging

logger = logging.getLogger(__name__)


@login_required
def drink_list(request):
    """
    Viser drinks baseret på brugerens bar ingredienser.
    """
    user_bar, created = UserBar.objects.get_or_create(user=request.user)
    selected_ingredients = set(user_bar.ingredients.values_list('id', flat=True))

    # Check om brugeren kan bestille drinks (er requester i en aktiv profil)
    can_request_drinks = DrinkRequestProfile.objects.filter(
        requester_user=request.user,
        is_active=True
    ).exists()

    # Hent bruger-specifik data
    favorite_ids = set(UserFavorite.objects.filter(user=request.user).values_list('drink_id', flat=True))
    user_ratings = {r.drink_id: r.rating for r in DrinkRating.objects.filter(user=request.user)}

    complete_drinks = []
    substitute_drinks = []
    almost_drinks = []

    if selected_ingredients:
        all_drinks = Drink.objects.select_related().prefetch_related(
            'drink_ingredients__ingredient__substitutes',
            'drink_ingredient_groups__ingredient_group__ingredients',
            'ratings'  # For average rating calculation
        ).all()

        for drink in all_drinks:
            missing_ingredients = []
            substitute_info = []

            # Check direct required ingredients
            for req in drink.drink_ingredients.all():
                if req.is_required and req.ingredient.id not in selected_ingredients:
                    # Check for substitutes
                    available_substitutes = [sub for sub in req.ingredient.substitutes.all()
                                           if sub.id in selected_ingredients]
                    if available_substitutes:
                        substitute_info.append({
                            'original': req.ingredient.name,
                            'substitutes': [sub.name for sub in available_substitutes]
                        })
                    else:
                        missing_ingredients.append(req.ingredient)

            # Check required ingredient groups
            for req_group in drink.drink_ingredient_groups.all():
                if req_group.is_required:
                    group_ingredient_ids = set(req_group.ingredient_group.ingredients.values_list('id', flat=True))
                    if not selected_ingredients.intersection(group_ingredient_ids):
                        missing_ingredients.append(req_group.ingredient_group)

            # Add user-specific data
            drink.favorite = drink.id in favorite_ids
            drink.user_rating = user_ratings.get(drink.id)
            drink.average_rating = getattr(drink, 'get_average_rating', lambda: 0)()

            # Categorize drinks
            if len(missing_ingredients) == 0:
                if substitute_info:
                    drink.substitutions = substitute_info
                    substitute_drinks.append(drink)
                else:
                    complete_drinks.append(drink)
            elif len(missing_ingredients) == 1:
                drink.missing_ingredient = missing_ingredients[0].name
                almost_drinks.append(drink)

    context = {
        'complete_drinks': complete_drinks,
        'substitute_drinks': substitute_drinks,
        'almost_drinks': almost_drinks,
        'can_request_drinks': can_request_drinks,
    }
    return render(request, 'bar/drink_list.html', context)

@login_required
def drink_detail(request, drink_id):
    drink = get_object_or_404(Drink, id=drink_id)
    # Denne visning kræver en ny skabelon: drink_detail.html
    return render(request, 'bar/drink_detail.html', {'drink': drink})

@staff_member_required
def edit_drink(request, drink_id):
    # Denne visning sender dig til den eksisterende admin-side for at redigere drinken
    from django.urls import reverse
    return redirect(reverse('admin:bar_drink_change', args=[drink_id]))

@login_required
def my_bar(request):
    # Optimized: Only select needed fields and prefetch related data
    ingredients = Ingredient.objects.select_related().prefetch_related('substitutes').order_by('name')
    user_bar, created = UserBar.objects.get_or_create(user=request.user)

    if request.method == 'POST':
        selected_ids = request.POST.getlist('ingredients')
        user_bar.ingredients.set(selected_ids)
        return redirect('bar:my_bar')

    selected_ids = user_bar.ingredients.values_list('id', flat=True)

    context = {
        'ingredients': ingredients,
        'selected_ids': list(selected_ids),
    }
    return render(request, 'bar/my_bar.html', context)


@login_required
def add_comment(request):
    if request.method == 'POST':
        drink_id = request.POST.get('drink_id')
        comment_text = request.POST.get('comment_text')
        try:
            drink = get_object_or_404(Drink, id=drink_id)
            if not comment_text.strip():
                return JsonResponse({'status': 'error', 'message': 'Comment cannot be empty'}, status=400)

            comment = Comment.objects.create(
                user=request.user,
                drink=drink,
                text=comment_text
            )
            return JsonResponse({
                'status': 'success',
                'comment': {
                    'username': request.user.username,
                    'text': comment.text,
                    'created_at': comment.created_at.strftime('%Y-%m-%d %H:%M')
                }
            })
        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)}, status=500)
    return JsonResponse({'status': 'error', 'message': 'Invalid request'}, status=400)


@login_required
def get_comments(request, drink_id):
    try:
        drink = get_object_or_404(Drink, id=drink_id)
        comments = drink.comments.all().order_by('-created_at')
        comments_data = [{
            'author': comment.user.username,
            'user': comment.user.username,  # Backup field name
            'text': comment.text,
            'date': comment.created_at.strftime('%d/%m/%Y'),
            'created_at': comment.created_at.strftime('%d/%m/%Y %H:%M')
        } for comment in comments]
        return JsonResponse({'success': True, 'comments': comments_data})
    except Exception as e:
        logger.error(f"Error in get_comments: {e}")
        return JsonResponse({'success': False, 'error': str(e)}, status=500)


@login_required
def get_drink_recipe(request, drink_id):
    try:
        drink = get_object_or_404(Drink, id=drink_id)
        return JsonResponse({
            'status': 'success',
            'recipe': drink.get_full_recipe()
        })
    except Exception as e:
        return JsonResponse({'status': 'error', 'message': str(e)}, status=500)


# DRINK PLANNER VIEWS - Easy to remove if not wanted
@login_required
def drink_planner(request):
    user_bar, created = UserBar.objects.get_or_create(user=request.user)
    selected_ingredients = user_bar.ingredients.all()

    # Get drinks user can make
    can_make_drinks = []
    all_drinks = Drink.objects.all()

    for drink in all_drinks:
        missing = []
        for ing in drink.required_ingredients.all():
            if ing not in selected_ingredients:
                missing.append(ing.name)
        for drink_group in drink.drink_ingredient_groups.all():
            if not drink_group.is_required:
                continue
            group = drink_group.ingredient_group
            if not group.ingredients.filter(id__in=selected_ingredients.values_list('id', flat=True)).exists():
                missing.append(group.name)

        if len(missing) == 0:
            can_make_drinks.append(drink)

    # Get user's existing plans
    user_plans = DrinkPlan.objects.filter(user=request.user).order_by('-created_at')

    context = {
        'can_make_drinks': can_make_drinks,
        'user_plans': user_plans,
    }
    return render(request, 'bar/drink_planner.html', context)


@login_required
def create_drink_plan(request):
    if request.method == 'POST':
        try:
            plan_name = request.POST.get('plan_name')
            guest_count = int(request.POST.get('guest_count'))
            selected_drinks = request.POST.getlist('selected_drinks')

            if not plan_name or guest_count <= 0 or not selected_drinks:
                return JsonResponse({'status': 'error', 'message': 'Alle felter skal udfyldes'})

            # Create the plan
            plan = DrinkPlan.objects.create(
                user=request.user,
                name=plan_name,
                guest_count=guest_count
            )

            # Add drinks to plan
            for drink_id in selected_drinks:
                drink = get_object_or_404(Drink, id=drink_id)
                servings = float(request.POST.get(f'servings_{drink_id}', 1.0))
                DrinkPlanItem.objects.create(
                    plan=plan,
                    drink=drink,
                    servings_per_guest=servings
                )

            return JsonResponse({'status': 'success', 'plan_id': plan.id})

        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})

    return JsonResponse({'status': 'error', 'message': 'Invalid request method'})


@login_required
def calculate_ingredients(request):
    if request.method == 'POST':
        try:
            selected_drinks = request.POST.getlist('selected_drinks')

            if not selected_drinks:
                return JsonResponse({'status': 'error', 'message': 'Ingen drinks valgt'})

            # Dictionary to store total ingredient amounts
            total_ingredients = {}
            drink_details = []

            for drink_data in selected_drinks:
                drink_id, servings = drink_data.split(':')
                drink = get_object_or_404(Drink, id=drink_id)
                servings = int(servings)

                # Get ingredients for this drink
                drink_ingredients = DrinkIngredient.objects.filter(drink=drink)
                drink_ingredient_groups = drink.drink_ingredient_groups.all()
                drink_ingredient_list = []

                # Process individual ingredients
                for drink_ing in drink_ingredients:
                    ingredient_name = drink_ing.ingredient.name
                    total_amount = float(drink_ing.amount) * servings
                    unit = drink_ing.unit

                    # Add to drink details
                    drink_ingredient_list.append({
                        'name': ingredient_name,
                        'amount_per_serving': float(drink_ing.amount),
                        'total_amount': total_amount,
                        'unit': unit
                    })

                    # Add to total ingredients
                    key = f"{ingredient_name}_{unit}"
                    if key in total_ingredients:
                        total_ingredients[key]['total_amount'] += total_amount
                    else:
                        total_ingredients[key] = {
                            'name': ingredient_name,
                            'total_amount': total_amount,
                            'unit': unit
                        }

                # Process ingredient groups
                for drink_group in drink_ingredient_groups:
                    group_name = f"{drink_group.ingredient_group.name} (valgfri)"
                    total_amount = float(drink_group.amount) * servings
                    unit = drink_group.unit

                    # Add to drink details
                    drink_ingredient_list.append({
                        'name': group_name,
                        'amount_per_serving': float(drink_group.amount),
                        'total_amount': total_amount,
                        'unit': unit
                    })

                    # Add to total ingredients
                    key = f"{group_name}_{unit}"
                    if key in total_ingredients:
                        total_ingredients[key]['total_amount'] += total_amount
                    else:
                        total_ingredients[key] = {
                            'name': group_name,
                            'total_amount': total_amount,
                            'unit': unit
                        }

                drink_details.append({
                    'name': drink.name,
                    'servings': servings,
                    'ingredients': drink_ingredient_list
                })

            # Convert to list and sort
            total_ingredients_list = list(total_ingredients.values())
            total_ingredients_list.sort(key=lambda x: x['name'])

            return JsonResponse({
                'status': 'success',
                'drink_details': drink_details,
                'total_ingredients': total_ingredients_list
            })

        except Exception as e:
            return JsonResponse({'status': 'error', 'message': str(e)})

    return JsonResponse({'status': 'error', 'message': 'Invalid request method'})


# ===== DRINK IMPORT VIEWS =====

@staff_member_required
def import_drinks_page(request):
    """Show the drink import interface"""
    return render(request, 'bar/import_drinks.html')


@staff_member_required
@require_POST
@csrf_exempt
def import_single_drink(request):
    """Import a single drink from URL"""
    try:
        data = json.loads(request.body)
        url = data.get('url')

        if not url:
            return JsonResponse({'success': False, 'error': 'URL er påkrævet'})

        # Use the management command to import
        from io import StringIO
        import sys

        # Capture output
        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()

        try:
            call_command('import_drinks', '--url', url)
            output = captured_output.getvalue()

            # Clean up ANSI escape codes
            import re
            clean_output = re.sub(r'\x1b\[[0-9;]*[mGKHF]', '', output)
            clean_output = re.sub(r'\[\?[0-9]+[hl]', '', clean_output)

            # Check if import was successful
            if '✅ Imported:' in clean_output:
                # Extract drink name
                match = re.search(r'✅ Imported:\s*(.+)', clean_output)
                if match:
                    drink_name = match.group(1).strip()
                    return JsonResponse({
                        'success': True,
                        'drink_name': drink_name,
                        'message': f'Drink "{drink_name}" importeret succesfuldt!'
                    })
            elif 'already exists' in clean_output:
                # Drink already exists
                match = re.search(r'Drink\s+(.+?)\s+already exists', clean_output)
                if match:
                    drink_name = match.group(1).strip()
                    return JsonResponse({
                        'success': True,
                        'drink_name': drink_name,
                        'message': f'Drink "{drink_name}" eksisterer allerede!'
                    })

            # If we get here, something went wrong
            logger.error(f"Import output: {clean_output}")

            # Check for specific error patterns
            if 'Unknown Drink' in clean_output:
                return JsonResponse({
                    'success': False,
                    'error': 'Kunne ikke finde drink navn på siden. Tjek om URL\'en er korrekt.'
                })
            elif 'No ingredients found' in clean_output or 'ingredients: []' in clean_output:
                return JsonResponse({
                    'success': False,
                    'error': 'Kunne ikke finde ingredienser på siden. Måske en anden side struktur?'
                })
            elif 'HTTP' in clean_output and 'Error' in clean_output:
                return JsonResponse({
                    'success': False,
                    'error': 'Kunne ikke hente siden. Tjek om URL\'en eksisterer.'
                })
            else:
                # Return part of the output for debugging
                debug_output = clean_output[-200:] if len(clean_output) > 200 else clean_output
                return JsonResponse({
                    'success': False,
                    'error': f'Import fejlede. Debug: {debug_output}'
                })

        finally:
            sys.stdout = old_stdout

    except Exception as e:
        logger.error(f"Import error: {str(e)}")
        # Check if it's a module import error
        if 'No module named' in str(e):
            return JsonResponse({
                'success': False,
                'error': f'Manglende modul: {str(e)}. Kontakt administrator.'
            })
        else:
            return JsonResponse({
                'success': False,
                'error': f'Fejl ved import: {str(e)}'
            })


@staff_member_required
@require_POST
@csrf_exempt
def bulk_import_drinks(request):
    """Import multiple drinks from a category"""
    try:
        data = json.loads(request.body)
        category = data.get('category', 'cocktails')
        limit = data.get('limit', 10)

        # Use the management command for bulk import
        from io import StringIO
        import sys

        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()

        try:
            call_command('import_drinks', '--category', category, '--limit', str(limit))
            output = captured_output.getvalue()

            # Count successful imports
            import_count = output.count('✅ Imported:')

            return JsonResponse({
                'success': True,
                'imported_count': import_count,
                'message': f'{import_count} drinks importeret fra {category}'
            })

        finally:
            sys.stdout = old_stdout

    except Exception as e:
        logger.error(f"Bulk import error: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'Fejl ved bulk import: {str(e)}'
        })


@staff_member_required
@require_POST
@csrf_exempt
def preview_import(request):
    """Preview what would be imported without actually importing"""
    try:
        data = json.loads(request.body)
        category = data.get('category', 'cocktails')
        limit = data.get('limit', 10)

        # Use dry-run mode to preview
        from io import StringIO
        import sys

        old_stdout = sys.stdout
        sys.stdout = captured_output = StringIO()

        try:
            call_command('import_drinks', '--category', category, '--limit', str(limit), '--dry-run')
            output = captured_output.getvalue()

            # Parse the dry-run output to extract drink info
            drinks = []
            lines = output.split('\n')
            current_drink = {}

            for line in lines:
                if line.startswith('🍹 '):
                    if current_drink:
                        drinks.append(current_drink)
                    current_drink = {'name': line[2:].strip()}
                elif line.startswith('📝 ') and current_drink:
                    current_drink['description'] = line[2:].strip()
                elif line.startswith('📋 Ingredienser:') and current_drink:
                    current_drink['ingredients'] = []
                elif line.startswith('  ') and 'ingredients' in current_drink:
                    current_drink['ingredients'].append(line.strip())

            if current_drink:
                drinks.append(current_drink)

            return JsonResponse({
                'success': True,
                'drinks': drinks,
                'count': len(drinks)
            })

        finally:
            sys.stdout = old_stdout

    except Exception as e:
        logger.error(f"Preview error: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'Fejl ved preview: {str(e)}'
        })


@staff_member_required
def quick_add_drink_page(request):
    """Show the quick add drink interface"""
    return render(request, 'bar/quick_add_drink.html')


@staff_member_required
@require_POST
@csrf_exempt
def quick_add_drink_api(request):
    """API endpoint for quick drink addition"""
    try:
        # Get form data
        name = request.POST.get('name')
        drink_type = request.POST.get('drink_type', 'drink')
        description = request.POST.get('description', '')
        instructions = request.POST.get('instructions', '')
        ingredients_json = request.POST.get('ingredients', '[]')

        if not name:
            return JsonResponse({'success': False, 'error': 'Drink navn er påkrævet'})

        # Parse ingredients
        try:
            ingredients_data = json.loads(ingredients_json)
        except:
            ingredients_data = []

        with transaction.atomic():
            # Create drink
            drink = Drink.objects.create(
                name=name,
                drink_type=drink_type,
                description=description,
                instructions=instructions
            )

            # Handle image upload
            if 'image' in request.FILES:
                drink.image = request.FILES['image']
                drink.save()

            # Add ingredients
            for ing_data in ingredients_data:
                # Get or create ingredient
                ingredient, created = Ingredient.objects.get_or_create(
                    name=ing_data['name'],
                    defaults={'description': f'Tilføjet via hurtig import'}
                )

                # Create drink ingredient relationship
                DrinkIngredient.objects.create(
                    drink=drink,
                    ingredient=ingredient,
                    amount=ing_data.get('amount') or 1,
                    unit=ing_data.get('unit', 'stk'),
                    required=ing_data.get('required', True)
                )

            return JsonResponse({
                'success': True,
                'drink_name': drink.name,
                'drink_id': drink.id,
                'message': f'Drink "{drink.name}" er tilføjet med {len(ingredients_data)} ingredienser!'
            })

    except Exception as e:
        logger.error(f"Quick add drink error: {str(e)}")
        return JsonResponse({
            'success': False,
            'error': f'Fejl ved tilføjelse: {str(e)}'
        })

# Views from drinks.py consolidated here

@login_required
@require_POST
def toggle_favorite(request):
    """Toggle favorit-status for en drink."""
    try:
        data = json.loads(request.body)
        drink_id = data.get('drink_id')
    except json.JSONDecodeError:
        return JsonResponse({'status': 'error', 'message': 'Invalid JSON'}, status=400)

    if not drink_id:
        return JsonResponse({'status': 'error', 'message': 'Manglende drink ID'}, status=400)

    drink = get_object_or_404(Drink, id=drink_id)

    favorite, created = UserFavorite.objects.get_or_create(
        user=request.user,
        drink=drink
    )

    if not created:
        favorite.delete()
        is_favorite = False
    else:
        is_favorite = True

    return JsonResponse({'status': 'success', 'is_favorite': is_favorite})

@login_required
@require_POST
def rate_drink(request):
    """Gem eller opdater brugerens bedømmelse af en drink."""
    try:
        data = json.loads(request.body)
        drink_id = data.get('drink_id')
        rating = data.get('rating')
    except json.JSONDecodeError:
        return JsonResponse({'status': 'error', 'message': 'Invalid JSON'}, status=400)

    if not drink_id or rating is None:
        return JsonResponse({'status': 'error', 'message': 'Manglende data'}, status=400)

    try:
        rating = int(rating)
        if not (1 <= rating <= 5):
            raise ValueError("Rating skal være mellem 1 og 5")
    except (ValueError, TypeError):
        return JsonResponse({'status': 'error', 'message': 'Ugyldig rating'}, status=400)

    drink = get_object_or_404(Drink, id=drink_id)

    obj, created = DrinkRating.objects.update_or_create(
        user=request.user,
        drink=drink,
        defaults={'rating': rating}
    )

    return JsonResponse({'status': 'success', 'rating': rating, 'is_new': created})

@login_required
def get_user_ratings(request):
    """Hent alle brugerens bedømmelser."""
    ratings = DrinkRating.objects.filter(user=request.user).select_related('drink')
    data = [{'drink_id': rating.drink.id,
        'drink_name': rating.drink.name,
        'rating': rating.rating,
        'updated_at': rating.updated_at.isoformat()
    } for rating in ratings]
    return JsonResponse({'status': 'success', 'ratings': data})

@login_required
def get_user_favorites(request):
    """Hent alle brugerens favoritter."""
    favorites = UserFavorite.objects.filter(user=request.user).select_related('drink')
    data = [{'drink_id': favorite.drink.id,
        'drink_name': favorite.drink.name,
        'created_at': favorite.created_at.isoformat()
    } for favorite in favorites]
    return JsonResponse({'status': 'success', 'favorites': data})


# API ENDPOINTS FOR MODAL RATINGS AND COMMENTS

@login_required
@require_POST
def rate_drink_api(request):
    """API endpoint for rating drinks from modal."""
    try:
        data = json.loads(request.body)
        drink_id = data.get('drink_id')
        rating = data.get('rating')

        if not drink_id or not rating:
            return JsonResponse({'success': False, 'error': 'Missing drink_id or rating'})

        if not (1 <= int(rating) <= 5):
            return JsonResponse({'success': False, 'error': 'Rating must be between 1 and 5'})

        drink = get_object_or_404(Drink, id=drink_id)

        # Create or update rating
        rating_obj, created = DrinkRating.objects.update_or_create(
            user=request.user,
            drink=drink,
            defaults={'rating': int(rating)}
        )

        # Calculate new average
        average_rating = drink.get_average_rating()

        return JsonResponse({
            'success': True,
            'rating': rating_obj.rating,
            'average_rating': average_rating,
            'created': created
        })

    except Exception as e:
        logger.error(f"Error in rate_drink_api: {e}")
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
@require_POST
def add_comment_api(request):
    """API endpoint for adding comments from modal."""
    try:
        data = json.loads(request.body)
        drink_id = data.get('drink_id')
        text = data.get('text', '').strip()

        if not drink_id or not text:
            return JsonResponse({'success': False, 'error': 'Missing drink_id or text'})

        drink = get_object_or_404(Drink, id=drink_id)

        # Create comment
        comment = Comment.objects.create(
            user=request.user,
            drink=drink,
            text=text
        )

        return JsonResponse({
            'success': True,
            'comment': {
                'id': comment.id,
                'text': comment.text,
                'author': comment.user.username,
                'created_at': comment.created_at.strftime('%d/%m/%Y %H:%M')
            }
        })

    except Exception as e:
        logger.error(f"Error in add_comment_api: {e}")
        return JsonResponse({'success': False, 'error': str(e)})


# SIMPLIFIED DRINK REQUEST SYSTEM

@login_required
@require_POST
def request_drink(request):
    """API endpoint til at bestille en drink"""
    try:
        data = json.loads(request.body)
        drink_id = data.get('drink_id')
        guest_name = data.get('guest_name', '').strip()
        table_number = data.get('table_number', '').strip()
        special_notes = data.get('special_notes', '').strip()

        if not drink_id:
            return JsonResponse({'success': False, 'error': 'Missing drink_id'})

        # Check at brugeren kan bestille drinks
        profile = DrinkRequestProfile.objects.filter(
            requester_user=request.user,
            is_active=True
        ).first()

        if not profile:
            return JsonResponse({'success': False, 'error': 'Du har ikke tilladelse til at bestille drinks'})

        drink = get_object_or_404(Drink, id=drink_id)

        # Opret drink request
        drink_request = DrinkRequest.objects.create(
            profile=profile,
            drink=drink,
            requester=request.user,
            guest_name=guest_name,
            table_number=table_number,
            special_notes=special_notes
        )

        return JsonResponse({
            'success': True,
            'request_id': drink_request.id,
            'message': f'Drink request for {drink.name} er sendt til {profile.bartender_user.username}!'
        })

    except Exception as e:
        logger.error(f"Error in request_drink: {e}")
        return JsonResponse({'success': False, 'error': str(e)})





@login_required
def bartender_dashboard(request):
    """Dashboard for bartendere til at se og håndtere drink requests"""
    # Kun vis requests fra profiler hvor brugeren er bartender
    profiles = DrinkRequestProfile.objects.filter(bartender_user=request.user, is_active=True)

    # Check for AJAX update request
    if request.GET.get('ajax') == '1':
        last_update = request.GET.get('last_update')
        if last_update:
            try:
                last_update_time = datetime.fromtimestamp(int(last_update) / 1000)
                # Check if there are any requests created OR updated since last check
                has_updates = DrinkRequest.objects.filter(
                    profile__in=profiles
                ).filter(
                    Q(requested_at__gt=last_update_time) |
                    Q(started_at__gt=last_update_time) |
                    Q(completed_at__gt=last_update_time)
                ).exists()
                return JsonResponse({'has_updates': has_updates})
            except (ValueError, TypeError):
                pass
        return JsonResponse({'has_updates': False})

    # Hent alle requests fra disse profiler med prefetch for performance
    pending_requests = DrinkRequest.objects.filter(
        profile__in=profiles,
        status='pending'
    ).select_related('drink', 'profile', 'requester').prefetch_related(
        'drink__drink_ingredients__ingredient',
        'drink__drink_ingredient_groups__ingredient_group__ingredients'
    ).order_by('requested_at')

    in_progress_requests = DrinkRequest.objects.filter(
        profile__in=profiles,
        status='in_progress'
    ).select_related('drink', 'profile', 'requester').prefetch_related(
        'drink__drink_ingredients__ingredient',
        'drink__drink_ingredient_groups__ingredient_group__ingredients'
    ).order_by('started_at')

    completed_requests = DrinkRequest.objects.filter(
        profile__in=profiles,
        status='completed'
    ).select_related('drink', 'profile', 'requester').prefetch_related(
        'drink__drink_ingredients__ingredient',
        'drink__drink_ingredient_groups__ingredient_group__ingredients'
    ).order_by('-completed_at')[:20]  # Sidste 20

    context = {
        'profiles': profiles,
        'pending_requests': pending_requests,
        'in_progress_requests': in_progress_requests,
        'completed_requests': completed_requests,
    }
    return render(request, 'bar/bartender_dashboard.html', context)


@login_required
@require_POST
def update_request_status(request):
    """API endpoint til at opdatere status på drink requests"""
    try:
        data = json.loads(request.body)
        request_id = data.get('request_id')
        new_status = data.get('status')

        if not request_id or not new_status:
            return JsonResponse({'success': False, 'error': 'Missing request_id or status'})

        # Hent request og check at brugeren har adgang
        drink_request = get_object_or_404(DrinkRequest, id=request_id)

        # Check at brugeren er bartender for denne profil
        if drink_request.profile.bartender_user != request.user:
            return JsonResponse({'success': False, 'error': 'Ingen adgang til denne request'})

        # Opdater status og timestamps
        from django.utils import timezone

        if new_status == 'in_progress' and drink_request.status == 'pending':
            drink_request.status = 'in_progress'
            drink_request.started_at = timezone.now()
            drink_request.assigned_bartender = request.user
        elif new_status == 'completed':
            drink_request.status = 'completed'
            drink_request.completed_at = timezone.now()
        elif new_status == 'cancelled':
            drink_request.status = 'cancelled'
        else:
            return JsonResponse({'success': False, 'error': 'Ugyldig status transition'})

        drink_request.save()

        return JsonResponse({
            'success': True,
            'request_id': drink_request.id,
            'new_status': drink_request.status,
            'status_display': drink_request.get_status_display()
        })

    except Exception as e:
        logger.error(f"Error in update_request_status: {e}")
        return JsonResponse({'success': False, 'error': str(e)})


@login_required
def check_my_requests(request):
    """API endpoint for requesters to check their drink request status"""
    try:
        # Find user's requests from last 2 hours (including completed/cancelled)
        from django.utils import timezone
        two_hours_ago = timezone.now() - timezone.timedelta(hours=2)

        my_requests = DrinkRequest.objects.filter(
            requester=request.user,
            requested_at__gte=two_hours_ago
        ).select_related('drink', 'profile').order_by('-requested_at')

        requests_data = []
        for req in my_requests:
            requests_data.append({
                'id': req.id,
                'drink_name': req.drink.name,
                'status': req.status,
                'status_display': req.get_status_display(),
                'requested_at': req.requested_at.strftime('%H:%M'),
                'guest_name': req.guest_name or 'Anonym',
                'table_number': req.table_number or '',
                'started_at': req.started_at.strftime('%H:%M') if req.started_at else None,
                'completed_at': req.completed_at.strftime('%H:%M') if req.completed_at else None,
            })

        return JsonResponse({
            'success': True,
            'requests': requests_data
        })

    except Exception as e:
        logger.error(f"Error in check_my_requests: {e}")
        return JsonResponse({'success': False, 'error': str(e)})


def pwa_manifest(request):
    """Serve PWA manifest with dynamic content"""
    from django.http import JsonResponse

    manifest = {
        "name": "MyBar - Drink Management",
        "short_name": "MyBar",
        "description": "Professional drink management and ordering system",
        "start_url": "/",
        "display": "standalone",
        "background_color": "#667eea",
        "theme_color": "#667eea",
        "orientation": "portrait-primary",
        "scope": "/",
        "lang": "da",
        "categories": ["food", "lifestyle", "productivity"],
        "icons": [
            {
                "src": "/static/bar/icons/icon-192x192.png",
                "sizes": "192x192",
                "type": "image/png",
                "purpose": "maskable any"
            },
            {
                "src": "/static/bar/icons/icon-512x512.png",
                "sizes": "512x512",
                "type": "image/png",
                "purpose": "maskable any"
            }
        ]
    }

    return JsonResponse(manifest)


@login_required
def pwa_offline(request):
    """Offline fallback page for PWA"""
    return render(request, 'bar/pwa_offline.html')
