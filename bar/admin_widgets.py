from django import forms
from django.utils.safestring import mark_safe
from django.utils.html import format_html


class ImagePositionWidget(forms.NumberInput):
    """Custom widget for image positioning with visual slider"""
    
    def __init__(self, attrs=None, min_val=0, max_val=100, step=1, unit='%'):
        self.min_val = min_val
        self.max_val = max_val
        self.step = step
        self.unit = unit
        super().__init__(attrs)
    
    def render(self, name, value, attrs=None, renderer=None):
        if value is None:
            value = 50  # Default to center
            
        # Get the base input field
        input_html = super().render(name, value, attrs, renderer)
        
        # Create the slider and preview
        widget_html = format_html('''
        <div class="image-position-widget" style="margin: 10px 0;">
            <div style="display: flex; align-items: center; gap: 15px;">
                <div style="flex: 1;">
                    <input type="range" 
                           min="{min_val}" 
                           max="{max_val}" 
                           step="{step}"
                           value="{value}"
                           style="width: 100%; height: 8px; border-radius: 5px; background: linear-gradient(90deg, #667eea, #764ba2); outline: none;"
                           oninput="document.getElementById('{name}').value = this.value; document.getElementById('{name}_display').innerText = this.value + '{unit}';">
                </div>
                <div style="min-width: 60px; text-align: center;">
                    <span id="{name}_display" style="font-weight: bold; color: #667eea;">{value}{unit}</span>
                </div>
            </div>
            <div style="margin-top: 5px;">
                {input_html}
            </div>
        </div>
        <script>
            // Sync slider with input field
            document.getElementById('{name}').addEventListener('input', function() {{
                const slider = this.parentElement.parentElement.querySelector('input[type="range"]');
                const display = document.getElementById('{name}_display');
                slider.value = this.value;
                display.innerText = this.value + '{unit}';
            }});
        </script>
        ''', 
        min_val=self.min_val,
        max_val=self.max_val, 
        step=self.step,
        value=value,
        unit=self.unit,
        name=attrs.get('id', name) if attrs else name,
        input_html=input_html
        )
        
        return mark_safe(widget_html)


class ImageZoomWidget(ImagePositionWidget):
    """Custom widget for image zoom with visual slider"""
    
    def __init__(self, attrs=None):
        super().__init__(attrs, min_val=50, max_val=200, step=5, unit='%')


class ImagePreviewWidget(forms.Widget):
    """Widget that shows live preview of image positioning"""

    def render(self, name, value, attrs=None, renderer=None):
        # Get the drink instance if available
        widget_html = format_html('''
        <div class="image-preview-container" style="margin: 20px 0; padding: 20px; border: 2px solid #667eea; border-radius: 15px; background: linear-gradient(135deg, #f8f9ff, #fff);">
            <h3 style="color: #667eea; margin-bottom: 15px;">📸 Live Billede Preview</h3>
            <div style="display: flex; gap: 20px; align-items: flex-start;">
                <div style="flex: 1;">
                    <div id="imagePreviewCard" style="width: 200px; height: 150px; border-radius: 15px; overflow: hidden; position: relative; background: linear-gradient(135deg, #667eea, #764ba2); box-shadow: 0 4px 16px rgba(31, 38, 135, 0.2);">
                        <img id="previewImage" src="" style="width: 100%; height: 100%; object-fit: cover; object-position: 50% 50%; transform: scale(1);" />
                        <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: 10px; background: linear-gradient(transparent, rgba(0,0,0,0.8)); color: white; text-align: center; font-size: 12px;">
                            Preview
                        </div>
                    </div>
                </div>
                <div style="flex: 1; font-size: 12px; color: #666;">
                    <p><strong>Sådan justerer du billedet:</strong></p>
                    <ul style="margin: 10px 0; padding-left: 20px;">
                        <li><strong>Position X:</strong> Vandret placering (0% = venstre, 50% = center, 100% = højre)</li>
                        <li><strong>Position Y:</strong> Lodret placering (0% = top, 50% = center, 100% = bund)</li>
                        <li><strong>Zoom:</strong> Størrelse (50% = lille, 100% = normal, 200% = stor)</li>
                    </ul>
                    <p style="color: #667eea;"><strong>💡 Tip:</strong> Ændringer vises øjeblikkeligt i preview!</p>
                </div>
            </div>
        </div>

        <script>
        function updateImagePreview() {{
            const posX = document.querySelector('input[name="image_position_x"]');
            const posY = document.querySelector('input[name="image_position_y"]');
            const zoom = document.querySelector('input[name="image_zoom"]');
            const previewImg = document.getElementById('previewImage');

            if (posX && posY && zoom && previewImg) {{
                const xVal = posX.value || 50;
                const yVal = posY.value || 50;
                const zoomVal = (zoom.value || 100) / 100;

                previewImg.style.objectPosition = xVal + '% ' + yVal + '%';
                previewImg.style.transform = 'scale(' + zoomVal + ')';
            }}
        }}

        // Update preview when sliders change
        document.addEventListener('DOMContentLoaded', function() {{
            const inputs = ['image_position_x', 'image_position_y', 'image_zoom'];
            inputs.forEach(function(inputName) {{
                const input = document.querySelector('input[name="' + inputName + '"]');
                const slider = input ? input.parentElement.parentElement.querySelector('input[type="range"]') : null;

                if (input) {{
                    input.addEventListener('input', updateImagePreview);
                }}
                if (slider) {{
                    slider.addEventListener('input', updateImagePreview);
                }}
            }});

            // Set initial image if available
            const imageField = document.querySelector('input[name="image"]');
            if (imageField && imageField.value) {{
                document.getElementById('previewImage').src = '/media/' + imageField.value;
            }}

            // Update preview initially
            setTimeout(updateImagePreview, 100);
        }});
        </script>
        ''')

        return mark_safe(widget_html)


class ImagePositionForm(forms.ModelForm):
    """Custom form for Drink model with image positioning widgets"""

    class Meta:
        widgets = {
            'image_position_x': ImagePositionWidget(),
            'image_position_y': ImagePositionWidget(),
            'image_zoom': ImageZoomWidget(),
        }
