from django.contrib.auth.models import User
from .models import Drink, Ingredient, DrinkRating, UserBar

def admin_stats(request):
    """
    Context processor to add statistics to admin dashboard
    """
    if request.path.startswith('/admin/') and request.path == '/admin/':
        try:
            stats = {
                'total_drinks': Drink.objects.count(),
                'total_ingredients': Ingredient.objects.count(),
                'total_users': User.objects.count(),
                'total_ratings': DrinkRating.objects.count(),
                'total_bars': UserBar.objects.count(),
            }
            return stats
        except:
            # In case of database errors during migrations
            return {
                'total_drinks': 0,
                'total_ingredients': 0,
                'total_users': 0,
                'total_ratings': 0,
                'total_bars': 0,
            }
    return {}
