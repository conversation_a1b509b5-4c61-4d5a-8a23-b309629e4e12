/* Custom Admin Styling - Modern & Beautiful */

/* Import Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&display=swap');

/* Global Admin Styling */
body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
    min-height: 100vh;
}

/* Header Styling */
#header {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1) !important;
}

#branding h1 {
    color: #2c3e50 !important;
    font-weight: 700 !important;
    font-size: 28px !important;
    text-shadow: none !important;
}

#branding h1 a:link, #branding h1 a:visited {
    color: #2c3e50 !important;
}

/* Navigation */
#user-tools {
    background: transparent !important;
    padding: 10px 20px !important;
}

#user-tools a {
    color: #2c3e50 !important;
    font-weight: 500 !important;
    text-decoration: none !important;
    padding: 8px 16px !important;
    border-radius: 20px !important;
    transition: all 0.3s ease !important;
}

#user-tools a:hover {
    background: rgba(102, 126, 234, 0.1) !important;
    color: #667eea !important;
}

/* Main Content Area */
#content {
    background: rgba(255, 255, 255, 0.95) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 20px !important;
    margin: 20px !important;
    padding: 30px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

/* Breadcrumbs */
.breadcrumbs {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    padding: 15px 25px !important;
    border-radius: 15px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.breadcrumbs a {
    color: rgba(255, 255, 255, 0.9) !important;
    text-decoration: none !important;
    font-weight: 500 !important;
}

.breadcrumbs a:hover {
    color: white !important;
}

/* Module Headers */
.module h2, .module caption, .inline-group h2 {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    padding: 15px 20px !important;
    border-radius: 10px 10px 0 0 !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    margin: 0 !important;
    text-shadow: none !important;
    border: none !important;
}

/* Form Styling */
.form-row {
    background: rgba(248, 249, 250, 0.8) !important;
    border-radius: 10px !important;
    padding: 20px !important;
    margin-bottom: 15px !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
}

.form-row:hover {
    background: rgba(248, 249, 250, 1) !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
}

/* Input Fields */
input[type="text"], input[type="email"], input[type="password"], 
input[type="number"], textarea, select {
    border: 2px solid #e9ecef !important;
    border-radius: 8px !important;
    padding: 12px 16px !important;
    font-size: 14px !important;
    transition: all 0.3s ease !important;
    background: white !important;
}

input[type="text"]:focus, input[type="email"]:focus, 
input[type="password"]:focus, input[type="number"]:focus, 
textarea:focus, select:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1) !important;
    outline: none !important;
}

/* Buttons */
.default, input[type="submit"], .submit-row input, .button {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    border: none !important;
    padding: 12px 24px !important;
    border-radius: 8px !important;
    font-weight: 600 !important;
    font-size: 14px !important;
    cursor: pointer !important;
    transition: all 0.3s ease !important;
    text-decoration: none !important;
    display: inline-block !important;
}

.default:hover, input[type="submit"]:hover, 
.submit-row input:hover, .button:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.3) !important;
}

/* Delete Button */
.deletelink {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
    color: white !important;
}

.deletelink:hover {
    box-shadow: 0 8px 25px rgba(231, 76, 60, 0.3) !important;
}

/* Tables */
table {
    border-radius: 10px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    border: none !important;
}

thead th {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    font-weight: 600 !important;
    padding: 15px !important;
    border: none !important;
}

tbody tr {
    transition: all 0.3s ease !important;
}

tbody tr:hover {
    background: rgba(102, 126, 234, 0.05) !important;
}

tbody td {
    padding: 15px !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Inline Forms */
.inline-group {
    background: white !important;
    border-radius: 15px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    overflow: hidden !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.tabular.inline-related tbody tr {
    background: rgba(248, 249, 250, 0.5) !important;
}

.tabular.inline-related tbody tr:nth-child(even) {
    background: white !important;
}

/* Fieldsets */
fieldset {
    background: white !important;
    border-radius: 15px !important;
    margin-bottom: 25px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid rgba(0, 0, 0, 0.05) !important;
    padding: 0 !important;
}

fieldset h2 {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    margin: 0 !important;
    padding: 15px 20px !important;
    font-weight: 600 !important;
    border-radius: 15px 15px 0 0 !important;
}

fieldset .description {
    background: rgba(102, 126, 234, 0.05) !important;
    padding: 15px 20px !important;
    margin: 0 !important;
    color: #667eea !important;
    font-style: italic !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

/* Success Messages */
.messagelist .success {
    background: linear-gradient(135deg, #2ecc71, #27ae60) !important;
    color: white !important;
    border-radius: 10px !important;
    padding: 15px 20px !important;
    margin-bottom: 20px !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(46, 204, 113, 0.3) !important;
}

/* Error Messages */
.messagelist .error {
    background: linear-gradient(135deg, #e74c3c, #c0392b) !important;
    color: white !important;
    border-radius: 10px !important;
    padding: 15px 20px !important;
    margin-bottom: 20px !important;
    border: none !important;
    box-shadow: 0 4px 15px rgba(231, 76, 60, 0.3) !important;
}

/* Dashboard Styling */
.dashboard .module {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.9)) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 20px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    overflow: hidden !important;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    position: relative !important;
}

.dashboard .module::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea, #764ba2, #f093fb, #f5576c) !important;
    background-size: 300% 100% !important;
    animation: gradientShift 3s ease infinite !important;
}

@keyframes gradientShift {
    0%, 100% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
}

.dashboard .module:hover {
    transform: translateY(-8px) scale(1.02) !important;
    box-shadow: 0 16px 48px rgba(102, 126, 234, 0.2) !important;
}

/* Dashboard Module Content */
.dashboard .module h2 {
    background: transparent !important;
    color: #2c3e50 !important;
    padding: 20px 25px 15px !important;
    font-size: 18px !important;
    font-weight: 700 !important;
    margin: 0 !important;
    position: relative !important;
}

.dashboard .module h2::before {
    content: '';
    position: absolute;
    left: 25px;
    bottom: 0;
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2) !important;
    border-radius: 2px !important;
}

.dashboard .module table {
    margin: 0 !important;
    border: none !important;
}

.dashboard .module td {
    padding: 15px 25px !important;
    border: none !important;
    border-bottom: 1px solid rgba(0, 0, 0, 0.05) !important;
}

.dashboard .module td:last-child {
    border-bottom: none !important;
}

.dashboard .module a {
    color: #667eea !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
    display: flex !important;
    align-items: center !important;
    padding: 8px 0 !important;
}

.dashboard .module a:hover {
    color: #764ba2 !important;
    transform: translateX(5px) !important;
}

/* Add icons to dashboard links */
.dashboard .module a[href*="ingredient/"]:before { content: "🥃 "; }
.dashboard .module a[href*="drink/"]:before { content: "🍹 "; }
.dashboard .module a[href*="ingredientgroup/"]:before { content: "🏷️ "; }
.dashboard .module a[href*="userbar/"]:before { content: "👤 "; }
.dashboard .module a[href*="favorite/"]:before { content: "❤️ "; }
.dashboard .module a[href*="rating/"]:before { content: "⭐ "; }
.dashboard .module a[href*="comment/"]:before { content: "💬 "; }
.dashboard .module a[href*="drinkplan/"]:before { content: "🎉 "; }

/* Dashboard Welcome Message */
#content h1 {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    -webkit-background-clip: text !important;
    -webkit-text-fill-color: transparent !important;
    background-clip: text !important;
    font-size: 32px !important;
    font-weight: 800 !important;
    text-align: center !important;
    margin-bottom: 30px !important;
    text-shadow: none !important;
}

/* Add welcome stats */
.dashboard::before {
    content: "🍹 Velkommen til MyBar Admin Panel! Her kan du administrere drinks, ingredienser og meget mere.";
    display: block;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)) !important;
    border: 2px solid rgba(102, 126, 234, 0.2) !important;
    border-radius: 15px !important;
    padding: 20px 25px !important;
    margin-bottom: 30px !important;
    color: #667eea !important;
    font-weight: 600 !important;
    font-size: 16px !important;
    text-align: center !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.1) !important;
}

/* Sidebar Navigation */
#nav-sidebar {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95)) !important;
    backdrop-filter: blur(15px) !important;
    border-radius: 0 20px 20px 0 !important;
    box-shadow: 4px 0 20px rgba(0, 0, 0, 0.1) !important;
    border-right: 1px solid rgba(255, 255, 255, 0.2) !important;
    padding: 20px 0 !important;
}

.main-content {
    margin-left: 20px !important;
}

/* Navigation Links */
#nav-sidebar .app-bar {
    margin-bottom: 25px !important;
}

#nav-sidebar .app-bar .app-label {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    padding: 15px 20px !important;
    margin: 0 15px 10px !important;
    border-radius: 12px !important;
    font-weight: 700 !important;
    font-size: 16px !important;
    text-transform: uppercase !important;
    letter-spacing: 1px !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
    position: relative !important;
    overflow: hidden !important;
}

#nav-sidebar .app-bar .app-label::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent) !important;
    transition: left 0.5s ease !important;
}

#nav-sidebar .app-bar .app-label:hover::before {
    left: 100% !important;
}

#nav-sidebar .model-link {
    display: block !important;
    padding: 12px 25px !important;
    margin: 5px 15px !important;
    color: #2c3e50 !important;
    text-decoration: none !important;
    border-radius: 10px !important;
    transition: all 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275) !important;
    font-weight: 500 !important;
    position: relative !important;
    overflow: hidden !important;
}

#nav-sidebar .model-link::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1)) !important;
    transition: left 0.3s ease !important;
    z-index: -1 !important;
}

#nav-sidebar .model-link:hover {
    color: #667eea !important;
    transform: translateX(8px) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.2) !important;
}

#nav-sidebar .model-link:hover::before {
    left: 0 !important;
}

/* Add icons to navigation */
#nav-sidebar a[href*="ingredient/"]:before { content: "🥃 "; margin-right: 8px; }
#nav-sidebar a[href*="drink/"]:before { content: "🍹 "; margin-right: 8px; }
#nav-sidebar a[href*="ingredientgroup/"]:before { content: "🏷️ "; margin-right: 8px; }
#nav-sidebar a[href*="userbar/"]:before { content: "👤 "; margin-right: 8px; }
#nav-sidebar a[href*="favorite/"]:before { content: "❤️ "; margin-right: 8px; }
#nav-sidebar a[href*="rating/"]:before { content: "⭐ "; margin-right: 8px; }
#nav-sidebar a[href*="comment/"]:before { content: "💬 "; margin-right: 8px; }
#nav-sidebar a[href*="drinkplan/"]:before { content: "🎉 "; margin-right: 8px; }

/* Filter Sidebar */
#changelist-filter {
    background: linear-gradient(180deg, rgba(255, 255, 255, 0.95), rgba(248, 249, 250, 0.95)) !important;
    backdrop-filter: blur(15px) !important;
    border-radius: 15px !important;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    padding: 0 !important;
    overflow: hidden !important;
}

#changelist-filter h3 {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    padding: 15px 20px !important;
    margin: 0 !important;
    font-weight: 600 !important;
    position: relative !important;
}

#changelist-filter h3::after {
    content: '🔍';
    position: absolute;
    right: 20px;
    top: 50%;
    transform: translateY(-50%);
}

#changelist-filter ul {
    padding: 15px 0 !important;
    margin: 0 !important;
}

#changelist-filter li {
    padding: 8px 20px !important;
    transition: all 0.3s ease !important;
}

#changelist-filter li:hover {
    background: rgba(102, 126, 234, 0.1) !important;
}

#changelist-filter a {
    color: #2c3e50 !important;
    text-decoration: none !important;
    font-weight: 500 !important;
    transition: all 0.3s ease !important;
}

#changelist-filter a:hover {
    color: #667eea !important;
}

/* Responsive Design */
@media (max-width: 768px) {
    #content {
        margin: 10px !important;
        padding: 20px !important;
        border-radius: 15px !important;
    }
    
    .form-row {
        padding: 15px !important;
    }
}

/* Enhanced Tables */
.results table {
    border-collapse: separate !important;
    border-spacing: 0 8px !important;
    background: transparent !important;
}

.results tbody tr {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 12px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    transition: all 0.3s ease !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.results tbody tr:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15) !important;
    background: rgba(255, 255, 255, 1) !important;
}

.results tbody td {
    border: none !important;
    padding: 15px 20px !important;
    vertical-align: middle !important;
}

.results tbody td:first-child {
    border-radius: 12px 0 0 12px !important;
}

.results tbody td:last-child {
    border-radius: 0 12px 12px 0 !important;
}

/* Action Buttons */
.actions {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

.actions select, .actions input {
    border-radius: 8px !important;
    border: 2px solid #e9ecef !important;
    padding: 8px 12px !important;
    margin-right: 10px !important;
}

/* Search Box */
#searchbar {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-bottom: 20px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
}

#searchbar input[type="text"] {
    width: 100% !important;
    border: 2px solid #e9ecef !important;
    border-radius: 25px !important;
    padding: 15px 25px !important;
    font-size: 16px !important;
    background: white !important;
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

#searchbar input[type="text"]:focus {
    border-color: #667eea !important;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1), inset 0 2px 4px rgba(0, 0, 0, 0.05) !important;
}

/* Pagination */
.paginator {
    background: rgba(255, 255, 255, 0.9) !important;
    backdrop-filter: blur(10px) !important;
    border-radius: 15px !important;
    padding: 20px !important;
    margin-top: 20px !important;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05) !important;
    border: 1px solid rgba(255, 255, 255, 0.2) !important;
    text-align: center !important;
}

.paginator a, .paginator .this-page {
    display: inline-block !important;
    padding: 10px 15px !important;
    margin: 0 5px !important;
    border-radius: 8px !important;
    text-decoration: none !important;
    transition: all 0.3s ease !important;
    font-weight: 600 !important;
}

.paginator a {
    background: #f8f9fa !important;
    color: #667eea !important;
    border: 2px solid #e9ecef !important;
}

.paginator a:hover {
    background: #667eea !important;
    color: white !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.3) !important;
}

.paginator .this-page {
    background: linear-gradient(135deg, #667eea, #764ba2) !important;
    color: white !important;
    border: 2px solid transparent !important;
}

/* Custom Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.module, fieldset, .inline-group {
    animation: fadeInUp 0.6s ease-out !important;
}

/* Animated Icons */
.stat-icon, .quick-action-icon {
    animation: bounce 2s infinite !important;
}

.stat-card:hover .stat-icon,
.quick-action:hover .quick-action-icon {
    animation: pulse 0.5s ease-in-out !important;
}

/* Loading States */
.loading {
    position: relative !important;
    overflow: hidden !important;
}

.loading::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent) !important;
    animation: shimmer 1.5s infinite !important;
}

@keyframes shimmer {
    0% { left: -100%; }
    100% { left: 100%; }
}

/* Glassmorphism Effect */
.glass-effect {
    background: rgba(255, 255, 255, 0.25) !important;
    backdrop-filter: blur(10px) !important;
    border: 1px solid rgba(255, 255, 255, 0.18) !important;
}

/* Mobile Responsive */
@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: 1fr 1fr !important;
        gap: 15px !important;
    }

    .quick-actions {
        grid-template-columns: 1fr 1fr !important;
        gap: 10px !important;
    }

    .welcome-hero h1 {
        font-size: 28px !important;
    }

    .welcome-hero p {
        font-size: 16px !important;
    }

    .stat-number {
        font-size: 28px !important;
    }

    .stat-card {
        padding: 20px !important;
    }
}
