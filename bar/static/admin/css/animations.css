/* Animation Utilities for Drink List */
/* Animations CSS til brug i drink-list.html */

/* Fade In Animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out;
}

/* Fade In Up Animation */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out;
}

/* Fade In Down Animation */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-down {
    animation: fadeInDown 0.5s ease-out;
}

/* Staggered Animation for Lists */
.stagger-item {
    opacity: 0;
    transform: translateY(20px);
    animation: fadeInUp 0.5s ease-out forwards;
}

.stagger-item:nth-child(1) { animation-delay: 0.1s; }
.stagger-item:nth-child(2) { animation-delay: 0.15s; }
.stagger-item:nth-child(3) { animation-delay: 0.2s; }
.stagger-item:nth-child(4) { animation-delay: 0.25s; }
.stagger-item:nth-child(5) { animation-delay: 0.3s; }
.stagger-item:nth-child(6) { animation-delay: 0.35s; }
.stagger-item:nth-child(7) { animation-delay: 0.4s; }
.stagger-item:nth-child(8) { animation-delay: 0.45s; }
.stagger-item:nth-child(9) { animation-delay: 0.5s; }
.stagger-item:nth-child(10) { animation-delay: 0.55s; }
.stagger-item:nth-child(n+11) { animation-delay: 0.6s; }

/* Pulse Animation */
@keyframes pulse {
    0% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
    100% {
        transform: scale(1);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Shake Animation */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.8s cubic-bezier(.36,.07,.19,.97) both;
}

/* Bounce Animation */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-15px); }
    60% { transform: translateY(-7px); }
}

.bounce {
    animation: bounce 2s infinite;
}

/* Spin Animation */
@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.spin {
    animation: spin 2s linear infinite;
}

/* Slide In Left Animation */
@keyframes slideInLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-left {
    animation: slideInLeft 0.5s ease-out;
}
/* Animations CSS for BarMate */

/* Fade In animation */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-out forwards;
}

/* Bounce animation */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.bounce {
    animation: bounce 1s ease infinite;
}

/* Pulse animation */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 1.5s ease infinite;
}

/* Shake animation */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.8s ease-in-out;
}

/* Slide In animation */
@keyframes slideInUp {
    from { transform: translateY(20px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

.slide-in-up {
    animation: slideInUp 0.4s ease-out forwards;
}

/* Sorting animation */
@keyframes sortingHighlight {
    0% { background-color: rgba(255, 255, 255, 0); }
    30% { background-color: rgba(102, 126, 234, 0.2); }
    100% { background-color: rgba(255, 255, 255, 0); }
}

.sorting-highlight {
    animation: sortingHighlight 1s ease-out;
}

/* Sort button indicator */
@keyframes sortIndicator {
    0% { transform: translateX(0); }
    50% { transform: translateX(3px); }
    100% { transform: translateX(0); }
}

.sort-indicator {
    display: inline-block;
    animation: sortIndicator 1s ease infinite;
}

/* Loading animation */
@keyframes loading {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 3px solid rgba(255, 255, 255, 0.3);
    border-radius: 50%;
    border-top-color: #667eea;
    animation: loading 0.8s linear infinite;
    display: inline-block;
}

/* Transition effects */
.transition-fade {
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.transition-scale {
    transition: transform 0.3s ease;
}

.transition-color {
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Hover effects */
.hover-grow {
    transition: transform 0.2s ease;
}

.hover-grow:hover {
    transform: scale(1.05);
}

.hover-shadow {
    transition: box-shadow 0.3s ease;
}

.hover-shadow:hover {
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.1);
}

/* Drink item animation */
.drink-animation-enter {
    opacity: 0;
    transform: scale(0.9);
}

.drink-animation-enter-active {
    opacity: 1;
    transform: scale(1);
    transition: opacity 0.3s ease, transform 0.3s ease;
}

.drink-animation-exit {
    opacity: 1;
    transform: scale(1);
}

.drink-animation-exit-active {
    opacity: 0;
    transform: scale(0.9);
    transition: opacity 0.3s ease, transform 0.3s ease;
}
/* Slide In Right Animation */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

.slide-in-right {
    animation: slideInRight 0.5s ease-out;
}

/* Zoom In Animation */
@keyframes zoomIn {
    from {
        transform: scale(0.5);
        opacity: 0;
    }
    to {
        transform: scale(1);
        opacity: 1;
    }
}

.zoom-in {
    animation: zoomIn 0.5s ease-out;
}

/* Floating Animation */
@keyframes float {
    0% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0px);
    }
}

.float {
    animation: float 3s ease-in-out infinite;
}
/* Fade In */
@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

.fade-in {
    animation: fadeIn 0.5s ease-in-out forwards;
}

/* Fade In Up */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.fade-in-up {
    animation: fadeInUp 0.5s ease-out forwards;
}

/* Bounce */
@keyframes bounce {
    0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
    40% { transform: translateY(-10px); }
    60% { transform: translateY(-5px); }
}

.bounce {
    animation: bounce 2s infinite;
}

/* Pulse */
@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.05); }
    100% { transform: scale(1); }
}

.pulse {
    animation: pulse 2s infinite;
}

/* Shimmer */
@keyframes shimmer {
    0% { background-position: -1000px 0; }
    100% { background-position: 1000px 0; }
}

.shimmer {
    background: linear-gradient(to right, rgba(255,255,255,0) 0%, rgba(255,255,255,0.3) 50%, rgba(255,255,255,0) 100%);
    background-size: 1000px 100%;
    animation: shimmer 2s infinite linear;
}

/* Flip */
@keyframes flip {
    0% { transform: rotateY(0); }
    100% { transform: rotateY(180deg); }
}

.flip {
    animation: flip 0.6s ease-out forwards;
    transform-style: preserve-3d;
}

/* Scale In */
@keyframes scaleIn {
    from { transform: scale(0.8); opacity: 0; }
    to { transform: scale(1); opacity: 1; }
}

.scale-in {
    animation: scaleIn 0.5s ease-out forwards;
}

/* Float */
@keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
}

.float {
    animation: float 3s ease-in-out infinite;
}

/* Shake */
@keyframes shake {
    0%, 100% { transform: translateX(0); }
    10%, 30%, 50%, 70%, 90% { transform: translateX(-5px); }
    20%, 40%, 60%, 80% { transform: translateX(5px); }
}

.shake {
    animation: shake 0.8s ease-in-out;
}

/* Glow */
@keyframes glow {
    0%, 100% { box-shadow: 0 0 5px rgba(118, 75, 162, 0.5); }
    50% { box-shadow: 0 0 20px rgba(118, 75, 162, 0.8); }
}

.glow {
    animation: glow 2s ease-in-out infinite;
}
