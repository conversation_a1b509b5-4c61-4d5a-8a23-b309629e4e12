/* Live Preview Styles for Django Admin */

.live-preview-container {
    position: fixed;
    top: 20px;
    right: 20px;
    width: 300px;
    background: white;
    border: 2px solid #667eea;
    border-radius: 15px;
    padding: 20px;
    box-shadow: 0 8px 32px rgba(0,0,0,0.1);
    z-index: 9999;
    font-family: Arial, sans-serif;
}

.live-preview-container h3 {
    margin: 0 0 15px 0;
    color: #667eea;
    text-align: center;
    font-size: 16px;
}

.preview-card {
    width: 200px;
    height: 150px;
    margin: 0 auto 15px auto;
    border-radius: 15px;
    overflow: hidden;
    position: relative;
    background: linear-gradient(135deg, #667eea, #764ba2);
    box-shadow: 0 4px 16px rgba(31, 38, 135, 0.2);
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: cover;
    object-position: 50% 50%;
    position: absolute;
    top: 0;
    left: 0;
    transition: all 0.2s ease;
}

.preview-overlay {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px;
    background: linear-gradient(transparent, rgba(0,0,0,0.8));
    color: white;
    text-align: center;
    font-size: 12px;
    font-weight: 600;
}

.preview-values {
    font-size: 12px;
    color: #666;
    text-align: center;
    margin-bottom: 15px;
}

.preview-instructions {
    font-size: 11px;
    color: #888;
    line-height: 1.4;
}

.preview-close {
    position: absolute;
    top: 5px;
    right: 10px;
    background: none;
    border: none;
    font-size: 18px;
    color: #999;
    cursor: pointer;
}

.preview-close:hover {
    color: #667eea;
}

/* Enhanced slider styling for better UX */
.field-image_position_x .form-row,
.field-image_position_y .form-row,
.field-image_zoom .form-row {
    background: rgba(102, 126, 234, 0.05);
    padding: 10px;
    border-radius: 8px;
    margin-bottom: 10px;
}

/* Highlight active fields */
.field-image_position_x input:focus,
.field-image_position_y input:focus,
.field-image_zoom input:focus {
    border-color: #667eea;
    box-shadow: 0 0 5px rgba(102, 126, 234, 0.3);
}
