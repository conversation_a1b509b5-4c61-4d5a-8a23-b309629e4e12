document.addEventListener('DOMContentLoaded', function() {
    // Only run on drink change/add pages
    if (!window.location.pathname.includes('/bar/drink/')) return;
    
    console.log('🎨 Live Preview: Initializing...');
    
    // Find the image positioning fields
    const posXField = document.querySelector('input[name="image_position_x"]');
    const posYField = document.querySelector('input[name="image_position_y"]');
    const zoomField = document.querySelector('input[name="image_zoom"]');
    const imageField = document.querySelector('input[name="image"]');
    
    if (!posXField || !posYField || !zoomField) {
        console.log('❌ Live Preview: Required fields not found');
        return;
    }
    
    console.log('✅ Live Preview: Fields found, creating preview...');
    
    // Find the image settings fieldset and integrate preview
    const imageFieldset = document.querySelector('.field-image_position_x').closest('fieldset');
    if (!imageFieldset) {
        console.log('❌ Live Preview: Image fieldset not found');
        return;
    }

    // Create preview container integrated into fieldset
    const previewContainer = document.createElement('div');
    previewContainer.innerHTML = `
        <div style="background: linear-gradient(135deg, #f8f9ff, #fff); border: 2px solid #667eea; border-radius: 15px; padding: 20px; margin: 20px 0; box-shadow: 0 4px 16px rgba(102, 126, 234, 0.1);">
            <h3 style="margin: 0 0 15px 0; color: #667eea; text-align: center; font-size: 16px;">📸 Live Preview</h3>
            
            <!-- Drink Card Preview -->
            <div id="previewCard" style="width: 200px; height: 150px; margin: 0 auto 15px auto; border-radius: 15px; overflow: hidden; position: relative; background: linear-gradient(135deg, #667eea, #764ba2); box-shadow: 0 4px 16px rgba(31, 38, 135, 0.2);">
                <img id="previewImage" src="" style="width: 100%; height: 100%; object-fit: cover; object-position: 50% 50%; position: absolute; top: 0; left: 0;" />
                <div style="position: absolute; bottom: 0; left: 0; right: 0; padding: 10px; background: linear-gradient(transparent, rgba(0,0,0,0.8)); color: white; text-align: center; font-size: 12px; font-weight: 600;">
                    Preview
                </div>
            </div>
            
            <!-- Current Values Display -->
            <div style="font-size: 12px; color: #666; text-align: center; margin-bottom: 15px;">
                <div>X: <span id="currentX">50</span>% | Y: <span id="currentY">50</span>% | Zoom: <span id="currentZoom">100</span>%</div>
            </div>
            
            <!-- Instructions -->
            <div style="font-size: 11px; color: #888; line-height: 1.4;">
                <strong>Sådan justerer du:</strong><br>
                • X: 0% = venstre, 50% = center, 100% = højre<br>
                • Y: 0% = top, 50% = center, 100% = bund<br>
                • Zoom: 50% = lille, 100% = normal, 200% = stor
            </div>
            
        </div>
    `;

    // Insert preview into the image settings fieldset
    imageFieldset.appendChild(previewContainer);
    
    // Get preview elements
    const previewImage = document.getElementById('previewImage');
    const currentX = document.getElementById('currentX');
    const currentY = document.getElementById('currentY');
    const currentZoom = document.getElementById('currentZoom');
    
    // Update preview function
    function updatePreview() {
        const xVal = posXField.value || 50;
        const yVal = posYField.value || 50;
        const zoomVal = zoomField.value || 100;
        
        // Update image positioning
        previewImage.style.objectPosition = xVal + '% ' + yVal + '%';
        previewImage.style.transform = 'scale(' + (zoomVal / 100) + ')';
        
        // Update display values
        currentX.textContent = xVal;
        currentY.textContent = yVal;
        currentZoom.textContent = zoomVal;
        
        console.log(`🎨 Preview updated: X=${xVal}%, Y=${yVal}%, Zoom=${zoomVal}%`);
    }
    
    // Set initial image if available
    function setPreviewImage() {
        let imageSrc = '';

        // Try multiple ways to get the current image
        const currentImageLink = document.querySelector('.field-image .readonly a');
        const currentImageDiv = document.querySelector('.field-image .readonly');
        const imageInput = document.querySelector('input[name="image"]');

        // Method 1: From readonly link
        if (currentImageLink && currentImageLink.href) {
            imageSrc = currentImageLink.href;
        }
        // Method 2: From readonly div img tag
        else if (currentImageDiv) {
            const imgTag = currentImageDiv.querySelector('img');
            if (imgTag && imgTag.src) {
                imageSrc = imgTag.src;
            }
        }
        // Method 3: From URL if editing existing drink
        else if (window.location.pathname.includes('/change/')) {
            const drinkId = window.location.pathname.split('/').slice(-3, -2)[0];
            if (drinkId && !isNaN(drinkId)) {
                // Try to construct image URL
                imageSrc = `/media/drink_images/drink_${drinkId}.jpg`; // Common pattern
            }
        }

        // Fallback to placeholder
        if (!imageSrc || imageSrc.includes('None')) {
            imageSrc = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNjY3ZWVhIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSI0MCI+8J+NuTwvdGV4dD4KPC9zdmc+';
        }

        previewImage.src = imageSrc;
        console.log('🖼️ Preview image set:', imageSrc);

        // Handle image load errors
        previewImage.onerror = function() {
            console.log('❌ Image failed to load, using placeholder');
            this.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjIwMCIgdmlld0JveD0iMCAwIDIwMCAyMDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIiBmaWxsPSIjNjY3ZWVhIi8+Cjx0ZXh0IHg9IjEwMCIgeT0iMTEwIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSI0MCI+8J+NuTwvdGV4dD4KPC9zdmc+';
        };
    }
    
    // Add event listeners
    posXField.addEventListener('input', updatePreview);
    posYField.addEventListener('input', updatePreview);
    zoomField.addEventListener('input', updatePreview);
    
    // Also listen to range sliders if they exist
    const posXSlider = posXField.parentElement.querySelector('input[type="range"]');
    const posYSlider = posYField.parentElement.querySelector('input[type="range"]');
    const zoomSlider = zoomField.parentElement.querySelector('input[type="range"]');
    
    if (posXSlider) posXSlider.addEventListener('input', updatePreview);
    if (posYSlider) posYSlider.addEventListener('input', updatePreview);
    if (zoomSlider) zoomSlider.addEventListener('input', updatePreview);
    
    // Initialize
    setPreviewImage();
    updatePreview();
    
    console.log('✅ Live Preview: Fully initialized!');
});
