from django.urls import path
from . import views

app_name = 'bar'

urlpatterns = [
    path('', views.drink_list, name='drink_list'),
    path('drink/<int:drink_id>/', views.drink_detail, name='drink_detail'),
    path('drink/<int:drink_id>/edit/', views.edit_drink, name='edit_drink'),
    path('min-bar/', views.my_bar, name='my_bar'),
    path('add-comment/', views.add_comment, name='add_comment'),
    path('get-comments/<int:drink_id>/', views.get_comments, name='get_comments'),
    path('get-recipe/<int:drink_id>/', views.get_drink_recipe, name='get_drink_recipe'),

    # DRINK PLANNER URLs
    path('fest-planlægning/', views.drink_planner, name='drink_planner'),
    path('create-plan/', views.create_drink_plan, name='create_drink_plan'),
    path('calculate-ingredients/', views.calculate_ingredients, name='calculate_ingredients'),

    # DRINK REQUEST URLs
    path('api/request-drink/', views.request_drink, name='request_drink'),
    path('bartender-dashboard/', views.bartender_dashboard, name='bartender_dashboard'),
    path('api/update-request-status/', views.update_request_status, name='update_request_status'),
    path('api/check-my-requests/', views.check_my_requests, name='check_my_requests'),

    # PWA URLs
    path('manifest.json', views.pwa_manifest, name='pwa_manifest'),
    path('offline/', views.pwa_offline, name='pwa_offline'),

    # DRINK IMPORT URLs - Staff only
    path('import-drinks/', views.import_drinks_page, name='import_drinks'),
    path('api/import-drink/', views.import_single_drink, name='api_import_drink'),
    path('api/bulk-import/', views.bulk_import_drinks, name='api_bulk_import'),
    path('api/preview-import/', views.preview_import, name='api_preview_import'),

    # QUICK ADD DRINK URLs - Staff only
    path('hurtig-tilføj-drink/', views.quick_add_drink_page, name='quick_add_drink'),
    path('api/quick-add-drink/', views.quick_add_drink_api, name='api_quick_add_drink'),

    # Favorit og bedømmelsesfunktionalitet
    path('toggle-favorite/', views.toggle_favorite, name='toggle_favorite'),
    path('rate-drink/', views.rate_drink, name='rate_drink'),
    path('user-ratings/', views.get_user_ratings, name='user_ratings'),
    path('user-favorites/', views.get_user_favorites, name='user_favorites'),

    # API endpoints for modal ratings and comments
    path('api/rate-drink/', views.rate_drink_api, name='rate_drink_api'),
    path('api/add-comment/', views.add_comment_api, name='add_comment_api'),
]